package cn.iocoder.yudao.module.game.dal.mysql.config;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.game.dal.dataobject.config.GameConfigDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.game.controller.admin.config.vo.*;

/**
 * 游戏配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GameConfigMapper extends BaseMapperX<GameConfigDO> {

    default PageResult<GameConfigDO> selectPage(GameConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GameConfigDO>()
                .likeIfPresent(GameConfigDO::getName, reqVO.getName())
                .eqIfPresent(GameConfigDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(GameConfigDO::getGameType, reqVO.getGameType())
                .eqIfPresent(GameConfigDO::getTagIds, reqVO.getTagIds())
                .eqIfPresent(GameConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(GameConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(GameConfigDO::getId));
    }

}