package cn.iocoder.yudao.module.game.controller.app.reaction;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.game.controller.app.reaction.vo.*;
import cn.iocoder.yudao.module.game.dal.dataobject.reaction.GameReactionDO;
import cn.iocoder.yudao.module.game.service.reaction.GameReactionService;

@Tag(name = "用户 APP - 游戏互动")
@RestController
@RequestMapping("/game/reaction")
@Validated
public class AppGameReactionController {

    @Resource
    private GameReactionService reactionService;

    @PostMapping("/create")
    @Operation(summary = "创建游戏互动")
    public CommonResult<Long> createReaction(@Valid @RequestBody AppGameReactionSaveReqVO createReqVO) {
        return success(reactionService.createReaction(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新游戏互动")
    public CommonResult<Boolean> updateReaction(@Valid @RequestBody AppGameReactionSaveReqVO updateReqVO) {
        reactionService.updateReaction(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除游戏互动")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteReaction(@RequestParam("id") Long id) {
        reactionService.deleteReaction(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得游戏互动")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppGameReactionRespVO> getReaction(@RequestParam("id") Long id) {
        GameReactionDO reaction = reactionService.getReaction(id);
        return success(BeanUtils.toBean(reaction, AppGameReactionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得游戏互动分页")
    public CommonResult<PageResult<AppGameReactionRespVO>> getReactionPage(@Valid AppGameReactionPageReqVO pageReqVO) {
        PageResult<GameReactionDO> pageResult = reactionService.getReactionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppGameReactionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出游戏互动 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportReactionExcel(@Valid AppGameReactionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GameReactionDO> list = reactionService.getReactionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "游戏互动.xls", "数据", AppGameReactionRespVO.class,
                        BeanUtils.toBean(list, AppGameReactionRespVO.class));
    }

}