package cn.iocoder.yudao.module.game.dal.dataobject.rating;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 游戏评分 DO
 *
 * <AUTHOR>
 */
@TableName("game_rating")
@KeySequence("game_rating_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameRatingDO extends BaseDO {

    /**
     * 评分记录ID
     */
    @TableId
    private Long id;
    /**
     * 游戏ID
     */
    private Long gameId;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 评分1-5分
     */
    private Integer rating;

}