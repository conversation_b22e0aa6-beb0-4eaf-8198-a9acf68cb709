package cn.iocoder.yudao.module.game.convert;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.game.controller.admin.config.vo.GameConfigRespVO;
import cn.iocoder.yudao.module.game.dal.dataobject.category.GameCategoryDO;
import cn.iocoder.yudao.module.game.dal.dataobject.config.GameConfigDO;
import cn.iocoder.yudao.module.game.dal.dataobject.tag.GameTagDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * @program: ruoyi-vue-pro
 * @description: 游戏配置Convert
 * @author: DingXiao
 * @create: 2025-04-04 13:04
 **/
@Mapper
public interface GameConfigConvert {

    GameConfigConvert INSTANCE = Mappers.getMapper(GameConfigConvert.class);

    PageResult<GameConfigRespVO> convertPage(PageResult<GameConfigDO> page);

    default PageResult<GameConfigRespVO> convertPage(PageResult<GameConfigDO> pageResult,
                                                     List<GameTagDO> tags,
                                                     List<GameCategoryDO> categories) {
        PageResult<GameConfigRespVO> result = convertPage(pageResult);
        Map<Long, String> tagMap = convertMap(tags, GameTagDO::getId, GameTagDO::getName);
        Map<Long, String> categoryMap = convertMap(categories, GameCategoryDO::getId, GameCategoryDO::getName);
        // 填充关联数据
        result.getList().forEach(gameConfig -> {
            gameConfig.setTagNames(convertList(gameConfig.getTagIds(), tagMap::get));
            gameConfig.setCategoryName(categoryMap.get(gameConfig.getCategoryId()));

        });
        return result;

    }
}
