package cn.iocoder.yudao.module.game.dal.dataobject.config;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 游戏配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "game_config", autoResultMap = true)
@KeySequence("game_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameConfigDO extends BaseDO {

    /**
     * 分类编号
     */
    @TableId
    private Long id;
    /**
     * 游戏名称
     */
    private String name;
    /**
     * 游戏分类编号
     */
    private Long categoryId;
    /**
     * 游戏类型
     *
     * 枚举 {@link TODO game_type 对应的类}
     */
    private Integer gameType;
    /**
     * 游戏简介
     */
    private String introduction;
    /**
     * 游戏描述
     */
    private String description;
    /**
     * 游戏标签编号列表，以逗号分隔
     *
     * 枚举 {@link TODO game_tag 对应的类}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> tagIds;
    /**
     * 游戏链接
     */
    private String gameUrl;
    /**
     * like数
     */
    private Integer likesCount;
    /**
     * dislikes数
     */
    private Integer dislikesCount;
    /**
     * 游戏图片
     */
    private String picUrl;
    /**
     * 评分1-5分
     */
    private Integer rating;
    /**
     * 评分次数
     */
    private Integer ratingCount;
    /**
     * 评论次数
     */
    private Integer reviewCount;
    /**
     * 分类排序
     */
    private Integer sort;
    /**
     * 开启状态
     */
    private Integer status;

}