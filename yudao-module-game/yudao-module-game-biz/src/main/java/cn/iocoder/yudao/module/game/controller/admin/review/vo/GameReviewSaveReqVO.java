package cn.iocoder.yudao.module.game.controller.admin.review.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 游戏评论新增/修改 Request VO")
@Data
public class GameReviewSaveReqVO {

    @Schema(description = "评论记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12011")
    private Long id;

    @Schema(description = "游戏ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29129")
    @NotNull(message = "游戏ID不能为空")
    private Long gameId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30716")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "评论内容")
    private String content;

}