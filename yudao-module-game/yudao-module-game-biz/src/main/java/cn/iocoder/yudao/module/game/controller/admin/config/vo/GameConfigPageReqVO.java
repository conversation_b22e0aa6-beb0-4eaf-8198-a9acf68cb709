package cn.iocoder.yudao.module.game.controller.admin.config.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 游戏配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GameConfigPageReqVO extends PageParam {

    @Schema(description = "游戏名称", example = "王五")
    private String name;

    @Schema(description = "游戏分类编号", example = "19177")
    private Long categoryId;

    @Schema(description = "游戏类型", example = "2")
    private Integer gameType;

    @Schema(description = "游戏标签编号列表，以逗号分隔")
    private String tagIds;

    @Schema(description = "开启状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}