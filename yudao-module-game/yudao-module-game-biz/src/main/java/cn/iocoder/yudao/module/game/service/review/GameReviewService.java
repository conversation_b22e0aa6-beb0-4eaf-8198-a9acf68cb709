package cn.iocoder.yudao.module.game.service.review;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.module.game.controller.admin.review.vo.GameReviewPageReqVO;
import cn.iocoder.yudao.module.game.controller.admin.review.vo.GameReviewSaveReqVO;
import cn.iocoder.yudao.module.game.controller.app.review.vo.*;
import cn.iocoder.yudao.module.game.dal.dataobject.review.GameReviewDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 游戏评论 Service 接口
 *
 * <AUTHOR>
 */
public interface GameReviewService {

    //Admin
    /**
     * 创建游戏评论
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createReview(@Valid GameReviewSaveReqVO createReqVO);

    /**
     * 更新游戏评论
     *
     * @param updateReqVO 更新信息
     */
    void updateReview(@Valid GameReviewSaveReqVO updateReqVO);

    /**
     * 删除游戏评论
     *
     * @param id 编号
     */
    void deleteReview(Long id);

    /**
     * 获得游戏评论
     *
     * @param id 编号
     * @return 游戏评论
     */
    GameReviewDO getReview(Long id);

    /**
     * 获得游戏评论分页
     *
     * @param pageReqVO 分页查询
     * @return 游戏评论分页
     */
    PageResult<GameReviewDO> getReviewPage(GameReviewPageReqVO pageReqVO);

    //App

    /**
     * 创建游戏评论
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createReview(@Valid AppGameReviewSaveReqVO createReqVO);

    /**
     * 更新游戏评论
     *
     * @param updateReqVO 更新信息
     */
    void updateReview(@Valid AppGameReviewSaveReqVO updateReqVO);

    /**
     * 删除游戏评论
     *
     * @param id 编号
     */
    //void deleteReview(Long id);

    /**
     * 获得游戏评论
     *
     * @param id 编号
     * @return 游戏评论
     */
    //GameReviewDO getReview(Long id);

    /**
     * 获得游戏评论分页
     *
     * @param pageReqVO 分页查询
     * @return 游戏评论分页
     */
    PageResult<GameReviewDO> getReviewPage(AppGameReviewPageReqVO pageReqVO);

}