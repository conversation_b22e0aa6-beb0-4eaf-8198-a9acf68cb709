package cn.iocoder.yudao.module.game.controller.app.reaction.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 游戏互动新增/修改 Request VO")
@Data
public class AppGameReactionSaveReqVO {

    @Schema(description = "互动记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10671")
    private Long id;

    @Schema(description = "游戏ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16183")
    @NotNull(message = "游戏ID不能为空")
    private Long gameId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "20759")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "互动类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "互动类型不能为空")
    private Integer reactionType;

}