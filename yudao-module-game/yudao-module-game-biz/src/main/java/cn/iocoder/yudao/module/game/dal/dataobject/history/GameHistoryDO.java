package cn.iocoder.yudao.module.game.dal.dataobject.history;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 游戏历史 DO
 *
 * <AUTHOR>
 */
@TableName("game_history")
@KeySequence("game_history_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameHistoryDO extends BaseDO {

    /**
     * 历史记录ID
     */
    @TableId
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 游戏ID
     */
    private Long gameId;
    /**
     * 游戏时长（单位：分钟）
     */
    private Integer playedTime;

}