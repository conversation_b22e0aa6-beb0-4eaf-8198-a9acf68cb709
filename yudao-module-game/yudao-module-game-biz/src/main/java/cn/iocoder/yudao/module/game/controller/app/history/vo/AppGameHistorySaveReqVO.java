package cn.iocoder.yudao.module.game.controller.app.history.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 游戏历史新增/修改 Request VO")
@Data
public class AppGameHistorySaveReqVO {

    @Schema(description = "历史记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6917")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24296")
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    @Schema(description = "游戏ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13837")
    @NotNull(message = "游戏ID不能为空")
    private Long gameId;

    @Schema(description = "游戏时长（单位：分钟）")
    private Integer playedTime;

}