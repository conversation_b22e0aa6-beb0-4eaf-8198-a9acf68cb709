package cn.iocoder.yudao.module.game.controller.admin.review.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 游戏评论 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GameReviewRespVO {

    @Schema(description = "评论记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12011")
    @ExcelProperty("评论记录ID")
    private Long id;

    @Schema(description = "游戏ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29129")
    @ExcelProperty("游戏ID")
    private Long gameId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30716")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}