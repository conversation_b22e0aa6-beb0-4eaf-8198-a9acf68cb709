package cn.iocoder.yudao.module.erp.api.sale;

import cn.iocoder.yudao.module.erp.api.sale.dto.ErpSaleOrderSaveReqDTO;

/**
 * @program: ruoyi-vue-pro
 * @description: ERP 销售订单API
 * @author: DingXiao
 * @create: 2025-04-15 16:41
 **/
public interface ErpSaleOrderApi {

    /**
     * 创建销售订单
     *
     * @param reqDTO 创建信息
     * @return 生成的订单编号
     */
    Long createSaleOrder(ErpSaleOrderSaveReqDTO reqDTO);
}
