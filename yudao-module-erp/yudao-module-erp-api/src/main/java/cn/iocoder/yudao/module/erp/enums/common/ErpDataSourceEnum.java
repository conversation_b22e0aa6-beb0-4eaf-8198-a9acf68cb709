package cn.iocoder.yudao.module.erp.enums.common;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @program: ruoyi-vue-pro
 * @description: ERP 单据来源枚举
 * @author: Ding<PERSON>iao
 * @create: 2025-04-16 11:33
 **/
@RequiredArgsConstructor
@Getter
public enum ErpDataSourceEnum implements IntArrayValuable {

    MANUAL(0, "手动创建"),
    MALL(1, "商城");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ErpDataSourceEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 名称
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }
}