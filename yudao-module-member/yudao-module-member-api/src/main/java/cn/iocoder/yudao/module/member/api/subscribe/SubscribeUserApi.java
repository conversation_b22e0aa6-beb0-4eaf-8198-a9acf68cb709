package cn.iocoder.yudao.module.member.api.subscribe;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserPageApiReqDTO;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserRespDTO;

/**
 * @program: ruoyi-vue-pro
 * @description: 订阅用户API
 * @author: DingXiao
 * @create: 2025-04-03 14:24
 **/

public interface SubscribeUserApi {

    /**
     * 获取订阅用户分页
     *
     * @param pageReqVO 分页查询
     * @return 订阅用户分页
     */
    PageResult<SubscribeUserRespDTO> getSubscribeUserPage(SubscribeUserPageApiReqDTO pageReqVO);
}
