package cn.iocoder.yudao.module.member.framework.security.core;

import cn.iocoder.yudao.module.member.framework.security.config.PayPasswordSecurityProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 支付密码安全服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayPasswordSecurityService {

    private final PayPasswordSecurityRedisDAO payPasswordSecurityRedisDAO;
    private final PayPasswordSecurityProperties payPasswordSecurityProperties;

    /**
     * 检查用户是否被锁定
     *
     * @param userId 用户ID
     * @return 是否被锁定
     */
    public boolean isLocked(Long userId) {
        if (!payPasswordSecurityProperties.getEnabled()) {
            return false;
        }
        return payPasswordSecurityRedisDAO.isLocked(userId);
    }

    /**
     * 获取用户锁定剩余时间（秒）
     *
     * @param userId 用户ID
     * @return 剩余时间（秒）
     */
    public Long getLockRemainingTime(Long userId) {
        if (!payPasswordSecurityProperties.getEnabled()) {
            return 0L;
        }
        return payPasswordSecurityRedisDAO.getLockRemainingTime(userId);
    }

    /**
     * 记录支付密码验证失败
     *
     * @param userId 用户ID
     * @param userInfo 用户信息（用于日志记录）
     */
    public void recordPayPasswordError(Long userId, String userInfo) {
        if (!payPasswordSecurityProperties.getEnabled()) {
            return;
        }

        // 增加错误次数
        Integer errorCount = payPasswordSecurityRedisDAO.incrementErrorCount(userId, 
                payPasswordSecurityProperties.getResetDuration());

        log.warn("[支付密码验证失败] 用户: {}, 当前错误次数: {}/{}, IP: {}", 
                userInfo, errorCount, payPasswordSecurityProperties.getMaxErrorCount(), getClientIP());

        // 检查是否需要锁定
        if (errorCount >= payPasswordSecurityProperties.getMaxErrorCount()) {
            // 锁定用户
            payPasswordSecurityRedisDAO.lockUser(userId, payPasswordSecurityProperties.getLockDuration());
            
            log.error("[支付密码账户锁定] 用户: {}, 错误次数达到上限: {}, 锁定时间: {}分钟, IP: {}", 
                    userInfo, errorCount, payPasswordSecurityProperties.getLockTimeMinutes(), getClientIP());
        }
    }

    /**
     * 记录支付密码验证成功
     *
     * @param userId 用户ID
     */
    public void recordPayPasswordSuccess(Long userId) {
        if (!payPasswordSecurityProperties.getEnabled()) {
            return;
        }

        // 清除错误次数
        payPasswordSecurityRedisDAO.clearErrorCount(userId);
    }

    /**
     * 获取用户当前错误次数
     *
     * @param userId 用户ID
     * @return 错误次数
     */
    public Integer getErrorCount(Long userId) {
        if (!payPasswordSecurityProperties.getEnabled()) {
            return 0;
        }
        return payPasswordSecurityRedisDAO.getErrorCount(userId);
    }

    /**
     * 手动解锁用户（管理员操作）
     *
     * @param userId 用户ID
     */
    public void unlockUser(Long userId) {
        payPasswordSecurityRedisDAO.unlockUser(userId);
        payPasswordSecurityRedisDAO.clearErrorCount(userId);
        log.info("[支付密码手动解锁] 用户ID: {}", userId);
    }

    /**
     * 获取客户端IP地址
     * 这里简化处理，实际项目中可以通过ServletUtils获取
     */
    private String getClientIP() {
        try {
            return cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP();
        } catch (Exception e) {
            return "unknown";
        }
    }

}
