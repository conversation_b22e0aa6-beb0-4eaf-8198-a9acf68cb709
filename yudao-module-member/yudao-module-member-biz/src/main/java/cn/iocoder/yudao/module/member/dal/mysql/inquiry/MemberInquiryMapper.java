package cn.iocoder.yudao.module.member.dal.mysql.inquiry;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.member.dal.dataobject.inquiry.MemberInquiryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.member.controller.admin.inquiry.vo.*;

/**
 * 用户咨询 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberInquiryMapper extends BaseMapperX<MemberInquiryDO> {

    default PageResult<MemberInquiryDO> selectPage(MemberInquiryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberInquiryDO>()
                .likeIfPresent(MemberInquiryDO::getName, reqVO.getName())
                .eqIfPresent(MemberInquiryDO::getEmail, reqVO.getEmail())
                .eqIfPresent(MemberInquiryDO::getPhone, reqVO.getPhone())
                .eqIfPresent(MemberInquiryDO::getType, reqVO.getType())
                .eqIfPresent(MemberInquiryDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MemberInquiryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberInquiryDO::getId));
    }

}