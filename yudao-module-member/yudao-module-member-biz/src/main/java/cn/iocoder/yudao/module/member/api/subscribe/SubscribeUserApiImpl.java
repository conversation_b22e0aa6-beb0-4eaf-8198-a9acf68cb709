package cn.iocoder.yudao.module.member.api.subscribe;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserPageApiReqDTO;
import cn.iocoder.yudao.module.member.api.subscribe.dto.SubscribeUserRespDTO;
import cn.iocoder.yudao.module.member.convert.subscribe.SubscribeConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.subscribe.SubscribeDO;
import cn.iocoder.yudao.module.member.service.subscribe.SubscribeService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * @program: ruoyi-vue-pro
 * @description: 订阅用户API实现类
 * @author: DingXiao
 * @create: 2025-04-03 14:42
 **/
@Service
@Validated
public class SubscribeUserApiImpl implements SubscribeUserApi{

    @Resource
    SubscribeService subscribeService;

    @Override
    public PageResult<SubscribeUserRespDTO> getSubscribeUserPage(SubscribeUserPageApiReqDTO pageReqVO) {

        PageResult<SubscribeDO> subscribeUserPage = subscribeService.getSubscribeUserPage(pageReqVO);
        return SubscribeConvert.INSTANCE.convertPage(subscribeUserPage);
    }
}
