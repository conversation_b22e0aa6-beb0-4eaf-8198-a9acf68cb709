package cn.iocoder.yudao.module.member.controller.admin.adtracking.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 广告跟踪新增/修改 Request VO")
@Data
public class MemberAdTrackingSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21056")
    private Long id;

    @Schema(description = "用户编号", example = "23054")
    private Long userId;

    @Schema(description = "会话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18167")
    @NotEmpty(message = "会话ID不能为空")
    private String sessionId;

    @Schema(description = "广告来源")
    private String utmSource;

    @Schema(description = "广告媒介")
    private String utmMedium;

    @Schema(description = "广告活动")
    private String utmCampaign;

    @Schema(description = "广告关键词")
    private String utmTerm;

    @Schema(description = "广告内容")
    private String utmContent;

    @Schema(description = "Google点击ID", example = "19644")
    private String gclid;

    @Schema(description = "Facebook点击ID", example = "17793")
    private String fbclid;

    @Schema(description = "着陆页面")
    private String landingPage;

    @Schema(description = "来源页面")
    private String referrer;

    @Schema(description = "用户代理")
    private String userAgent;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "是否已转化")
    private Integer isConverted;

    @Schema(description = "转化类型", example = "1")
    private String conversionType;

    @Schema(description = "转化价值")
    private String conversionValue;

    @Schema(description = "转化时间")
    private String conversionTime;

}