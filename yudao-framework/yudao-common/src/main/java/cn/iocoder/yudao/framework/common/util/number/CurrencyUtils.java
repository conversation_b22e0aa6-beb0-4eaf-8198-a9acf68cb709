package cn.iocoder.yudao.framework.common.util.number;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @program: ruoyi-vue-pro
 * @description: 币别金额工具类
 * @author: DingXiao
 * @create: 2025-04-10 19:41
 **/
public class CurrencyUtils {

    // 转换分到元（单位：分 -> 元）
    public static BigDecimal convertFenToYuan(long fen) {
        return new BigDecimal(fen).divide(new BigDecimal(100));
    }

    // 转换元到分（单位：元 -> 分）
    public static int convertYuanToFen(BigDecimal yuan) {
        if (yuan == null) {
            return 0;
        }
        return yuan.multiply(new BigDecimal("100"))
                .setScale(0, RoundingMode.HALF_UP)
                .intValueExact();
    }

    // 根据汇率计算金额 (单位：分)
    public static int calculateAmountInTargetCurrency(long amountInFen, BigDecimal exchangeRate) {
        // 将金额转换为元进行计算，然后转换为目标币种金额
        BigDecimal amountInYuan = convertFenToYuan(amountInFen);
        BigDecimal targetAmountInYuan = amountInYuan.multiply(exchangeRate);

        // 将目标币种金额转换为分并返回
        return convertYuanToFen(targetAmountInYuan);
    }

    // 根据汇率计算金额并保留两位小数 (用于显示)
    public static BigDecimal calculateAmountInTargetCurrencyWithPrecision(long amountInFen, BigDecimal exchangeRate) {
        // 将金额转换为元进行计算
        BigDecimal amountInYuan = convertFenToYuan(amountInFen);

        // 计算目标币种金额，并保留两位小数（ROUND_HALF_UP：四舍五入）
        BigDecimal targetAmountInYuan = amountInYuan.multiply(exchangeRate);
        return targetAmountInYuan.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    // 转换目标币种金额到分 (用于实际金额存储计算)
    public static long convertAmountToFen(BigDecimal amountInTargetCurrency, BigDecimal exchangeRate) {
        BigDecimal amountInYuan = amountInTargetCurrency.divide(exchangeRate, 4, BigDecimal.ROUND_HALF_UP); // 先通过汇率换算为元
        return convertYuanToFen(amountInYuan);
    }

    public static void main(String[] args) {
        // 示例：汇率 1 CNY = 0.1505 USD
        BigDecimal exchangeRate = new BigDecimal("0.1505");

        // 示例：1000 分 (即10元)
        long amountInCNYFen = 1000;

        // 转换为目标货币（例如USD）的金额，单位为分
        long amountInUSDFen = calculateAmountInTargetCurrency(amountInCNYFen, exchangeRate);
        System.out.println("Amount in USD (in fen): " + amountInUSDFen);

        // 保留两位小数并显示目标币种金额
        BigDecimal amountInUSD = calculateAmountInTargetCurrencyWithPrecision(amountInCNYFen, exchangeRate);
        System.out.println("Amount in USD (with 2 decimal places): " + amountInUSD);

        // 假设用户支付了 10 美元，需要转换回人民币（单位：分）
        BigDecimal amountInUSDUser = new BigDecimal("10");
        long amountInCNYBackFen = convertAmountToFen(amountInUSDUser, exchangeRate);
        System.out.println("Amount in CNY (back to fen): " + amountInCNYBackFen);
    }
}
