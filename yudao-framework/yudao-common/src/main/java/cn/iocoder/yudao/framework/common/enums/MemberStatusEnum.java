package cn.iocoder.yudao.framework.common.enums;

/**
 * @program: ruoyi-vue-pro
 * @description: 会员状态枚举
 * @author: Ding<PERSON>iao
 * @create: 2024-11-12 17:53
 **/
public enum MemberStatusEnum {

    ENABLED(0, "已激活"),
    DISABLED(1, "已禁用"),
    UNVERIFIED(2, "未验证");

    private final Integer status;
    private final String name;

    MemberStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }
}
