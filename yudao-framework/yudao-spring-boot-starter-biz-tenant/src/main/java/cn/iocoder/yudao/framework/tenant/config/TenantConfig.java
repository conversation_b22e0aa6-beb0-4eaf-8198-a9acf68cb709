package cn.iocoder.yudao.framework.tenant.config;

import lombok.Data;

/**
 * @program: ruoyi-vue-pro
 * @description: 租户自己的配置
 * @author: DingXiao
 * @create: 2024-11-19 11:13
 **/
@Data
public class TenantConfig {


    private static final Boolean DISABLE_DEFAULT = false;
    private static final Boolean ENABLE_DEFAULT = true;

    /**
     * 租户类型：0-外贸，1-代购 5 游戏
     */
    private Integer type;
    /**
     * 新用户验证邮箱
     */
    private Boolean emailVerify = DISABLE_DEFAULT;

    /**
     * 注册真人验证
     */
    private Boolean registerHumanVerify = ENABLE_DEFAULT;

    /**
     * CloudFlare Turnstile 服务端密钥
     */
    private String turnstileSecretKey;

    //setReceiverName(address.getName()).setReceiverMobile(address.getMobile())
    //.setReceiverAreaId(address.getAreaId()).setReceiverDetailAddress(address.getDetailAddress());


    /**
     * 代购平台收货人信息
     */
    private String receiverName;

    /**
     * 代购平台收货人手机号
     */
    private String receiverMobile;

    /**
     * 代购平台收货人地区编号
     */
    private Integer receiverAreaId;

    /**
     * 代购平台收货人详细地址
     */
    private String receiverDetailAddress;

    /**
     * 收件人手机区号
     */
    private String receiverPhoneCode="86";
    /**
     * 收件人邮编
     */
    private String receiverPostCode="214000";
    /**
     * 收件人国家编码
     */
    private String receiverCountryCode="CN";

    /**
     * 是否开启站内信通知
     */
    private Boolean enableNotify = true;
    /**
     * 是否开启邮件通知
     */
    private Boolean enableEmail = true;

    private String shopName;

    private String supportEmail;

    private String domain;

    private String logoUrl;
    private String bannerUrl;

    private String fbUrl;
    private String tkUrl;
    private String insUrl;
    private String ttUrl;
    private String pinUrl;
    private String ytUrl;


    private Long erpUnitId;
    private Long erpAccountId;
    private Long erpCustomerId;
    private Long erpCategoryId;
    private Long erpSaleUserId;
    private Long erpSupplierId;



}
