package cn.iocoder.yudao.module.agent.service.price;

import cn.iocoder.yudao.module.agent.dal.dataobject.stock.AgentStockDO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;
import cn.iocoder.yudao.module.agent.service.price.calculator.AgentPriceCalculator;
import cn.iocoder.yudao.module.agent.service.price.calculator.AgentPriceCalculatorHelper;
import cn.iocoder.yudao.module.agent.service.stock.AgentStockService;
import cn.iocoder.yudao.module.product.api.sku.ProductSkuApi;
import cn.iocoder.yudao.module.product.api.sku.dto.ProductSkuRespDTO;
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.SKU_NOT_EXISTS;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.SKU_STOCK_NOT_ENOUGH;
import static cn.iocoder.yudao.module.trade.enums.ErrorCodeConstants.PRICE_CALCULATE_PAY_PRICE_ILLEGAL;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购包裹金额计算服务实现类
 * @author: DingXiao
 * @create: 2025-04-30 18:10
 **/
@Slf4j
@Validated
@Service
public class AgentPriceServiceImpl implements AgentPriceService{

    @Resource
    private ProductSkuApi productSkuApi;
    @Resource
    private ProductSpuApi productSpuApi;

    @Resource
    private List<AgentPriceCalculator> priceCalculators;


    @Override
    public AgentPriceCalculateRespBO calculateParcelPrice(AgentPriceCalculateReqBO calculateReqBO) {

        //// 1.1 获得商品 SKU 数组
        //List<ProductSkuRespDTO> skuList = checkSkuList(calculateReqBO);
        //// 1.2 获得商品 SPU 数组
        //List<ProductSpuRespDTO> spuList = checkSpuList(skuList);

        // 2.1 计算价格
        AgentPriceCalculateRespBO calculateRespBO = AgentPriceCalculatorHelper.buildCalculateResp(calculateReqBO);
        priceCalculators.forEach(calculator -> calculator.calculate(calculateReqBO, calculateRespBO));

        if (calculateRespBO.getPrice().getPayPrice() < 0) {
            log.error("[calculatePrice][价格计算不正确，请求 calculateReqDTO({})，结果 priceCalculate({})]", calculateReqBO, calculateRespBO);
            throw exception(PRICE_CALCULATE_PAY_PRICE_ILLEGAL);
        }

        return calculateRespBO;
    }

}
