package cn.iocoder.yudao.module.agent.service.price.calculator;

import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateReqBO;
import cn.iocoder.yudao.module.agent.service.price.bo.AgentPriceCalculateRespBO;

/**
 * @program: ruoyi-vue-pro
 * @description: 货运单价格计算的计算器接口
 * @author: Ding<PERSON><PERSON><PERSON>
 * @create: 2025-04-20 19:06
 **/
public interface AgentPriceCalculator {

    //int ORDER_SECKILL_ACTIVITY = 8;     //秒杀活动
    //int ORDER_BARGAIN_ACTIVITY = 8;     //砍价活动
    //int ORDER_COMBINATION_ACTIVITY = 8; //拼团活动
    //int ORDER_POINT_ACTIVITY = 8;       //积分商城

    //int ORDER_DISCOUNT_ACTIVITY = 10;   //限时折扣
    int ORDER_REWARD_ACTIVITY = 20;     //满减送活动





    /**
     * 运费的计算 为订单主要金额
     * 放在各种营销活动、优惠劵前面
     */
    int ORDER_DELIVERY = 30;            //运费

    int ORDER_COUPON = 40;              //优惠劵
    int ORDER_POINT_USE = 50;           //使用积分

    int ORDER_PLATFORM = 60;            //平台费
    /**
     * 保险费，放最后
     */
    int ORDER_INSURANCE = 70;

    /**
     * 服务费，放最后
     */
    int ORDER_SERVER = 80;              //增值服务


    /**
     * 赠送积分，放最后
     */
    int ORDER_POINT_GIVE = 999;         //赠送积分

    void calculate(AgentPriceCalculateReqBO param, AgentPriceCalculateRespBO result);
}
