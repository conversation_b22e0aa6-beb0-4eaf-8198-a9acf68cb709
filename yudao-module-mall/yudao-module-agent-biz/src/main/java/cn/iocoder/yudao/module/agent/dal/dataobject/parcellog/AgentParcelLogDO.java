package cn.iocoder.yudao.module.agent.dal.dataobject.parcellog;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代购包裹订单日志 DO
 *
 * <AUTHOR>
 */
@TableName("agent_parcel_log")
@KeySequence("agent_parcel_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentParcelLogDO extends BaseDO {

    /**
     * 用户类型 - 系统
     *
     * 例如说：Job 自动过期订单时，通过系统自动操作
     */
    public static final Integer USER_TYPE_SYSTEM = 0;
    /**
     * 用户编号 - 系统
     */
    public static final Long USER_ID_SYSTEM = 0L;

    /**
     * 日志主键
     */
    @TableId
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 用户类型
     */
    private Integer userType;
    /**
     * 包裹单号
     */
    private Long parcelId;
    /**
     * 操作前状态
     */
    private Integer beforeStatus;
    /**
     * 操作后状态
     */
    private Integer afterStatus;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 操作内容
     */
    private String content;

}