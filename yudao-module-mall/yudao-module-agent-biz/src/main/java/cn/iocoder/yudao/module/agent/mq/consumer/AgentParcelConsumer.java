package cn.iocoder.yudao.module.agent.mq.consumer;

import cn.hutool.core.lang.Validator;
import cn.iocoder.yudao.framework.common.enums.TenantTypeEnum;
import cn.iocoder.yudao.framework.common.util.number.MoneyUtils;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.framework.tenant.config.TenantConfig;
import cn.iocoder.yudao.framework.tenant.core.context.TenantConfigManager;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.service.parcel.AgentParcelQueryService;
import cn.iocoder.yudao.module.agent.utils.ParcelUtil;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.system.api.mail.MailSendApi;
import cn.iocoder.yudao.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.yudao.module.trade.message.TradeOrderSuccessMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * @program: ruoyi-vue-pro
 * @description: 包裹订单 消费者
 * @author: DingXiao
 * @create: 2025-05-21 21:39
 **/
@Slf4j
@Component
public class AgentParcelConsumer {

    public static final String TEMPLATE_PARCEL_SUCCESS = "parcel-success";

    @Resource
    AgentParcelQueryService parcelQueryService;

    @Resource
    MemberUserApi memberUserApi;

    @Resource
    MailSendApi mailSendApi;

    @EventListener
    @Async // Spring Event 默认在 Producer 发送的线程，通过 @Async 实现异步
    public void onMessage(TradeOrderSuccessMessage message) {
        log.info("[onMessage][收到 TradeOrderSuccessMessage({})]", message);

        AgentParcelDO order = parcelQueryService.getParcel(message.getOrderId());
        if (order == null) {
            return;
        }
        MemberUserRespDTO user = memberUserApi.getUser(order.getUserId());
        if (user == null) {
            return;
        }
        List<AgentParcelItemDO> orderItems = parcelQueryService.getParcelItemListByOrderId(message.getOrderId());
        TenantConfig tenantConfig = TenantConfigManager.getTenantConfig(getTenantId());


        //发送邮件
        if(tenantConfig.getEnableEmail()) {
            if(Validator.isEmail(user.getEmail())){
                HashMap<String, Object> params = new HashMap<>();
                //商品列表
                params.put("products",generateProductsDiv(orderItems));
                params.put("orderNo",order.getNo());
                //金额信息
                params.put("priceInfo",generatePriceInfoDiv(order));
                params.put("shippingAddress",generateAddressDiv(order));

                MailSendSingleToUserReqDTO mailDTO = new MailSendSingleToUserReqDTO();
                mailDTO.setUserId(user.getId());
                mailDTO.setMail(user.getEmail());
                mailDTO.setTemplateCode(getTenantId()+"-"+message.getLanguage()+"-"+TEMPLATE_PARCEL_SUCCESS);
                mailDTO.setTemplateParams(params);
                try {
                    mailSendApi.sendSingleMailToAdmin(mailDTO);
                }catch (Exception e){
                    log.error("邮件发送失败",e);
                }
            }
        }

    }

    private String generateProductsDiv(List<AgentParcelItemDO> orderItems) {
        StringBuilder result = new StringBuilder();
        //String div = "<tr> <td align=\"center\"> <img src=\"../images/template/1.jpg\" alt=\"\" width=\"80\"> </td> <td valign=\"top\" style=\"padding-left: 15px;\" width=\"250\"> <h5 style=\"font-size: 14px; margin-top: 15px; font-weight: normal\">Three seater Wood Style sofa for Leavingroom for Leavingroom</h5> </td> <td valign=\"top\" style=\"padding-left: 15px;\"> <h5 style=\"font-size: 14px; margin-top: 40px;\">QTY : <span>1</span></h5> </td> <td valign=\"top\" style=\"padding-left: 15px;\"> <h5 style=\"font-size: 14px; margin-top:40px\"><b>$500</b></h5> </td> </tr>";
        for (AgentParcelItemDO orderItem : orderItems) {
            result.append("<tr> <td align=\"center\" style=\"padding: 10px;border: 1px solid #ccc;\"> <img src=\""+orderItem.getPicUrl()+"\" alt=\""+orderItem.getSpuName()+"\" width=\"80\"> </td> <td valign=\"top\" style=\"padding-left: 15px;border: 1px solid #ccc;\" width=\"270\"> <h5 style=\"font-size: 14px; margin-top: 15px; font-weight: normal\">"+orderItem.getSpuName()+"<br>"+ ParcelUtil.formattedProperties(orderItem.getProperties()) +"</h5> </td> <td valign=\"top\" style=\"padding-left: 15px;border: 1px solid #ccc;\"> <h5 style=\"font-size: 14px; margin-top: 40px;\"><span>"+orderItem.getCount()+"</span></h5> </td> <td valign=\"top\" style=\"padding-left: 15px;border: 1px solid #ccc;\"> <h5 style=\"font-size: 14px; margin-top:40px\"><b>$"+ MoneyUtils.fenToYuanStr(orderItem.getPrice())+"</b></h5> </td> </tr>");
        }
        return result.toString();
    }

    private String generatePriceInfoDiv(AgentParcelDO order){
        StringBuilder result = new StringBuilder();
        if(order.getTotalPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Products: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+ MoneyUtils.fenToYuanStr(order.getTotalPrice())+"</b> </td> </tr>");
        }
        if(order.getDeliveryPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Shipping: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+MoneyUtils.fenToYuanStr(order.getDeliveryPrice())+"</b> </td> </tr>");
        }
        if(order.getDiscountPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Discount: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+MoneyUtils.fenToYuanStr(order.getDiscountPrice())+"</b> </td> </tr>");
        }
        if(order.getCouponPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Coupon: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+MoneyUtils.fenToYuanStr(order.getCouponPrice())+"</b> </td> </tr>");
        }
        if(order.getVipPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Vip Discount: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+MoneyUtils.fenToYuanStr(order.getVipPrice())+"</b> </td> </tr>");
        }
        if(order.getTotalPrice()>0){
            result.append("<tr> <td colspan=\"2\" style=\"line-height: 49px;font-size: 13px;padding-left: 20px;text-align:left;border: 1px solid #ccc;border-right: unset;\"> Total Paid: </td> <td colspan=\"3\" style=\"line-height: 49px;text-align: right;padding-right: 28px;font-size: 13px;text-align:right;border: 1px solid #ccc;border-left: unset;\"> <b>$"+MoneyUtils.fenToYuanStr(order.getPayPrice())+"</b> </td> </tr>");
        }
        return result.toString();
    }

    private String generateAddressDiv(AgentParcelDO order){
        StringBuilder result = new StringBuilder();
        result.append(order.getReceiverDetailAddress());
        result.append("<br>");
        result.append(AreaUtils.formatInternational(order.getReceiverAreaId()));
        result.append("<br>");
        result.append(order.getReceiverCountryCode()+" "+order.getReceiverPostCode());
        return result.toString();
    }
}
