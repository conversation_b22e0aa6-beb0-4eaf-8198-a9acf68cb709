package cn.iocoder.yudao.module.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重量比较类型枚举
 * 用于定义体积重和实际重量的比较规则
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum WeightCompareTypeEnum {

    /**
     * 取较大者
     * 包裹实际重量和体积重量相比，取较大者计算
     */
    MAX("MAX", "取较大者", "包裹实际重量和体积重量相比，取较大者计算"),

    /**
     * 双倍阈值规则
     * 体积重低于实际重量2倍的，按照实际重量收费；
     * 达到或超过实际重量2倍的，按照体积重量收取
     */
    DOUBLE_THRESHOLD("DOUBLE_THRESHOLD", "双倍阈值规则", 
                    "体积重低于实际重量2倍的，按照实际重量收费；达到或超过实际重量2倍的，按照体积重量收取");

    /**
     * 类型编码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值，如果不存在返回null
     */
    public static WeightCompareTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (WeightCompareTypeEnum value : values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 验证编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

    /**
     * 计算计费重量
     *
     * @param actualWeight 实际重量(g)
     * @param volumeWeight 体积重(g)
     * @return 计费重量(g)
     */
    public Integer calculateChargeableWeight(Integer actualWeight, Integer volumeWeight) {
        if (actualWeight == null || actualWeight <= 0) {
            return volumeWeight != null ? volumeWeight : 0;
        }
        
        if (volumeWeight == null || volumeWeight <= 0) {
            return actualWeight;
        }

        switch (this) {
            case MAX:
                return Math.max(actualWeight, volumeWeight);
                
            case DOUBLE_THRESHOLD:
                // 体积重低于实际重量2倍的，按照实际重量收费；达到或超过实际重量2倍的，按照体积重量收取
                return volumeWeight < actualWeight * 2 ? actualWeight : volumeWeight;
                
            default:
                return Math.max(actualWeight, volumeWeight);
        }
    }
}
