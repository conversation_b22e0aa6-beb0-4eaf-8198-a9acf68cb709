package cn.iocoder.yudao.module.agent.util;

import lombok.Data;

/**
 * 尺寸限制规则 POJO
 * 
 * 用于存储各种复杂的尺寸限制规则，支持不同国家的特殊要求
 * 
 * <AUTHOR>
 */
@Data
public class SizeLimitRule {

    // 最小尺寸（cm）
    private Integer minLength;
    private Integer minWidth;
    private Integer minHeight;

    // 最大尺寸（cm）
    private Integer maxLength;
    private Integer maxWidth;
    private Integer maxHeight;

    // 复杂规则（cm）
    /**
     * 单边最大长度限制
     * 例如：挪威最长边≤45CM
     */
    private Integer maxSingleSide;

    /**
     * 第二长边限制
     * 例如：加拿大第二边长≤76cm
     */
    private Integer maxSecondLongestSide;
    
    /**
     * 三边和限制（长+宽+高）
     * 例如：挪威长+宽+高≤90CM，土耳其长+宽+高≤90cm
     */
    private Integer maxTotalDimension;
    
    /**
     * 长 + 2*(宽+高) 限制
     * 例如：加拿大长+2*(宽+高)≤250cm
     */
    private Integer maxLengthPlusDoubleGirth;


    // 超尺寸处理
    /**
     * 超尺寸附加费（人民币分）
     * 例如：澳大利亚超尺寸附加费150RMB/票
     */
    private Integer oversizeFee;
    
    /**
     * 是否需要询价
     * 例如：澳大利亚超尺寸最大范围单询
     */
    private Boolean oversizeInquiryRequired = false;

    // 判断是否有某项限制的便捷方法
    
    /**
     * 是否有最小尺寸限制
     */
    public boolean hasMinDimensions() {
        return minLength != null || minWidth != null || minHeight != null;
    }
    
    /**
     * 是否有最大尺寸限制
     */
    public boolean hasMaxDimensions() {
        return maxLength != null || maxWidth != null || maxHeight != null;
    }

    /**
     * 是否有三边和限制
     */
    public boolean hasMaxDimensionSum() {
        return maxTotalDimension != null;
    }

    /**
     * 是否有长+2*(宽+高)限制
     */
    public boolean hasLengthPlusDoubleGirth() {
        return maxLengthPlusDoubleGirth != null;
    }

    /**
     * 是否有单边最大限制
     */
    public boolean hasMaxSingleSide() {
        return maxSingleSide != null;
    }
    
    /**
     * 是否有第二长边限制
     */
    public boolean hasMaxSecondLongestSide() {
        return maxSecondLongestSide != null;
    }
    
    /**
     * 是否有超尺寸费用
     */
    public boolean hasOversizeFee() {
        return oversizeFee != null && oversizeFee > 0;
    }
}
