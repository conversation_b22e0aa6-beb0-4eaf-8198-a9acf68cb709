package cn.iocoder.yudao.module.agent.service.message.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @program: ruoyi-vue-pro
 * @description: 包裹发货时通知创建 Req BO
 * @author: DingXiao
 * @create: 2025-05-31 14:09
 **/
@Data
public class AgentParcelMessageWhenDeliveryOrderReqBO {
    /**
     * 订单编号
     */
    @NotNull(message = "订单编号不能为空")
    private Long parcelId;
    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空")
    private Long userId;
    /**
     * 消息
     */
    @NotEmpty(message = "发送消息不能为空")
    private String message;
    //包裹编号
    private String parcelNo;
    private String transportNo;

    private String deliveryName;



}
