package cn.iocoder.yudao.module.agent.service.message;

import cn.iocoder.yudao.module.agent.enums.MessageTemplateConstants;
import cn.iocoder.yudao.module.agent.service.message.bo.AgentParcelMessageWhenDeliveryOrderReqBO;
import cn.iocoder.yudao.module.system.api.notify.NotifyMessageSendApi;
import cn.iocoder.yudao.module.system.api.notify.dto.NotifySendSingleToUserReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: ruoyi-vue-pro
 * @description: Agent消息 service 实现类
 * @author: DingXiao
 * @create: 2025-05-31 14:11
 **/
@Slf4j
@Service
@Validated
public class AgentMessageServiceImpl implements  AgentMessageService{

    @Resource
    private NotifyMessageSendApi notifyMessageSendApi;

    @Override
    public void sendMessageWhenDeliveryOrder(AgentParcelMessageWhenDeliveryOrderReqBO reqBO) {

        // 1、构造消息
        Map<String, Object> msgMap = new HashMap<>(2);
        msgMap.put("parcelNo", reqBO.getParcelNo());
        msgMap.put("deliveryName", reqBO.getDeliveryName());
        msgMap.put("transportNo", reqBO.getTransportNo());

        // 2、发送站内信
        notifyMessageSendApi.sendSingleMessageToMember(
                new NotifySendSingleToUserReqDTO()
                        .setUserId(reqBO.getUserId())
                        .setTemplateCode(MessageTemplateConstants.NOTICE_PARCEL_DELIVERY)
                        .setTemplateParams(msgMap));

        // 发送邮件 todo ding
        log.info("发送已发货邮件sendMessageWhenDeliveryOrder:{}", reqBO);

    }
}
