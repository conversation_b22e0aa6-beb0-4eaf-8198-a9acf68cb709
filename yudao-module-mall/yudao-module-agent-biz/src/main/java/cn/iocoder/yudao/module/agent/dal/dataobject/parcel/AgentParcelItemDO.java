package cn.iocoder.yudao.module.agent.dal.dataobject.parcel;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.io.Serializable;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代购包裹明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "agent_parcel_item", autoResultMap = true)
@KeySequence("agent_parcel_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentParcelItemDO extends BaseDO {

    /**
     * 品牌编号
     */
    @TableId
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 订单编号
     */
    private Long parcelId;
    /**
     * 库存编号
     */
    private Long stockId;
    /**
     * 商品 SPU 编号
     */
    private Long spuId;
    /**
     * 商品 SPU 名称
     */
    private String spuName;
    /**
     * 商品 SKU 编号
     */
    private Long skuId;

    /**
     * 属性数组，JSON 格式
     *
     * 冗余 ProductSkuDO 的 properties 字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Property> properties;
    /**
     * 商品图片
     */
    private String picUrl;
    /**
     * 订单数量
     */
    private Integer count;
    /**
     * 重量 单位：克
     */
    private Integer weight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;
    /**
     * 包装重量 单位：克
     */
    private Integer packingWeight;
    /**
     * 是否评价
     */
    private Boolean commentStatus;
    /**
     * 订单原价
     */
    private Integer price;
    /**
     * 商品级优惠
     */
    private Integer discountPrice;
    /**
     * 运费金额
     */
    private Integer deliveryPrice;
    /**
     * 订单调价
     */
    private Integer adjustPrice;
    /**
     * 保险金额
     */
    private Integer insurancePrice;
    /**
     * 服务费，单位：分
     */
    private Integer servicePrice;
    /**
     * 平台佣金，单位：分
     */
    private Integer platformPrice;
    /**
     * 子订单实付金额
     */
    private Integer payPrice;
    /**
     * 优惠劵减免金额
     */
    private Integer couponPrice;
    /**
     * 积分抵扣的金额，单位：分
     *
     * 对应 taobao 的 trade.point_fee 字段
     */
    private Integer pointPrice;
    /**
     * 使用的积分
     *
     * 目的：用于后续取消或者售后订单时，需要归还赠送
     */
    private Integer usePoint;
    /**
     * 赠送的积分
     */
    private Integer givePoint;
    /**
     * VIP 减免金额
     */
    private Integer vipPrice;
    /**
     * 售后订单编号
     */
    private Long afterSaleId;
    /**
     * 售后状态
     */
    private Integer afterSaleStatus;
    /**
     * 原订单编号
     */
    private Long sourceId;
    /**
     * 原订单项编号
     */
    private Long sourceItemId;

    @Data
    public static class Property implements Serializable {

        /**
         * 属性编号
         *
         * 关联 ProductPropertyDO 的 id 编号
         */
        private Long propertyId;
        /**
         * 属性名字
         *
         * 关联 ProductPropertyDO 的 name 字段
         */
        private String propertyName;

        /**
         * 属性值编号
         *
         * 关联 ProductPropertyValueDO 的 id 编号
         */
        private Long valueId;
        /**
         * 属性值名字
         *
         * 关联 ProductPropertyValueDO 的 name 字段
         */
        private String valueName;

    }

}