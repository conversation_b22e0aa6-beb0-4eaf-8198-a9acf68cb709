package cn.iocoder.yudao.module.agent.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 尺寸限制工具类
 * 支持多种尺寸限制检查：
 * 1. 最大长度、宽度、高度
 * 2. 最大周长（长+宽+高）
 * 3. 最大单边长度
 * 4. 体积限制
 *
 * <AUTHOR>
 */
@Slf4j
public class SizeRestrictionUtil {

    /**
     * 尺寸限制配置
     */
    public static class SizeRestriction {
        private BigDecimal maxLength;      // 最大长度(cm)
        private BigDecimal maxWidth;       // 最大宽度(cm)
        private BigDecimal maxHeight;      // 最大高度(cm)
        private BigDecimal maxGirth;       // 最大周长(cm) = 长+宽+高
        private BigDecimal maxSingleSide;  // 最大单边长度(cm)
        private BigDecimal maxVolume;      // 最大体积(cm³)

        // 构造函数
        public SizeRestriction() {}

        public SizeRestriction(BigDecimal maxLength, BigDecimal maxWidth, BigDecimal maxHeight, 
                             BigDecimal maxGirth, BigDecimal maxSingleSide, BigDecimal maxVolume) {
            this.maxLength = maxLength;
            this.maxWidth = maxWidth;
            this.maxHeight = maxHeight;
            this.maxGirth = maxGirth;
            this.maxSingleSide = maxSingleSide;
            this.maxVolume = maxVolume;
        }

        // Getters and Setters
        public BigDecimal getMaxLength() { return maxLength; }
        public void setMaxLength(BigDecimal maxLength) { this.maxLength = maxLength; }
        public BigDecimal getMaxWidth() { return maxWidth; }
        public void setMaxWidth(BigDecimal maxWidth) { this.maxWidth = maxWidth; }
        public BigDecimal getMaxHeight() { return maxHeight; }
        public void setMaxHeight(BigDecimal maxHeight) { this.maxHeight = maxHeight; }
        public BigDecimal getMaxGirth() { return maxGirth; }
        public void setMaxGirth(BigDecimal maxGirth) { this.maxGirth = maxGirth; }
        public BigDecimal getMaxSingleSide() { return maxSingleSide; }
        public void setMaxSingleSide(BigDecimal maxSingleSide) { this.maxSingleSide = maxSingleSide; }
        public BigDecimal getMaxVolume() { return maxVolume; }
        public void setMaxVolume(BigDecimal maxVolume) { this.maxVolume = maxVolume; }
    }

    /**
     * 尺寸检查结果
     */
    public static class SizeCheckResult {
        private boolean passed;
        private String errorMessage;
        private String errorCode;

        public SizeCheckResult(boolean passed, String errorMessage, String errorCode) {
            this.passed = passed;
            this.errorMessage = errorMessage;
            this.errorCode = errorCode;
        }

        public boolean isPassed() { return passed; }
        public String getErrorMessage() { return errorMessage; }
        public String getErrorCode() { return errorCode; }
    }

    /**
     * 解析尺寸限制配置
     *
     * @param sizeRestrictionsJson JSON格式的尺寸限制配置
     * @return 尺寸限制对象
     */
    public static SizeRestriction parseSizeRestrictions(String sizeRestrictionsJson) {
        if (StrUtil.isBlank(sizeRestrictionsJson)) {
            return null;
        }

        try {
            JSONObject json = JSONUtil.parseObj(sizeRestrictionsJson);
            SizeRestriction restriction = new SizeRestriction();
            
            if (json.containsKey("maxLength")) {
                restriction.setMaxLength(json.getBigDecimal("maxLength"));
            }
            if (json.containsKey("maxWidth")) {
                restriction.setMaxWidth(json.getBigDecimal("maxWidth"));
            }
            if (json.containsKey("maxHeight")) {
                restriction.setMaxHeight(json.getBigDecimal("maxHeight"));
            }
            if (json.containsKey("maxGirth")) {
                restriction.setMaxGirth(json.getBigDecimal("maxGirth"));
            }
            if (json.containsKey("maxSingleSide")) {
                restriction.setMaxSingleSide(json.getBigDecimal("maxSingleSide"));
            }
            if (json.containsKey("maxVolume")) {
                restriction.setMaxVolume(json.getBigDecimal("maxVolume"));
            }
            
            return restriction;
        } catch (Exception e) {
            log.error("解析尺寸限制配置失败: {}", sizeRestrictionsJson, e);
            return null;
        }
    }

    /**
     * 检查包裹尺寸是否符合限制
     *
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @param sizeRestrictionsJson 尺寸限制配置JSON
     * @return 检查结果
     */
    public static SizeCheckResult checkSize(BigDecimal length, BigDecimal width, BigDecimal height, 
                                          String sizeRestrictionsJson) {
        
        SizeRestriction restriction = parseSizeRestrictions(sizeRestrictionsJson);
        if (restriction == null) {
            // 没有限制配置，默认通过
            return new SizeCheckResult(true, null, null);
        }

        return checkSize(length, width, height, restriction);
    }

    /**
     * 检查包裹尺寸是否符合限制
     *
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @param restriction 尺寸限制对象
     * @return 检查结果
     */
    public static SizeCheckResult checkSize(BigDecimal length, BigDecimal width, BigDecimal height, 
                                          SizeRestriction restriction) {
        
        if (length == null || width == null || height == null) {
            return new SizeCheckResult(false, "包裹尺寸信息不完整", "INCOMPLETE_SIZE");
        }

        if (length.compareTo(BigDecimal.ZERO) <= 0 || 
            width.compareTo(BigDecimal.ZERO) <= 0 || 
            height.compareTo(BigDecimal.ZERO) <= 0) {
            return new SizeCheckResult(false, "包裹尺寸必须大于0", "INVALID_SIZE");
        }

        // 检查最大长度
        if (restriction.getMaxLength() != null && length.compareTo(restriction.getMaxLength()) > 0) {
            return new SizeCheckResult(false, 
                String.format("包裹长度%.1fcm超过限制%.1fcm", length, restriction.getMaxLength()), 
                "EXCEED_MAX_LENGTH");
        }

        // 检查最大宽度
        if (restriction.getMaxWidth() != null && width.compareTo(restriction.getMaxWidth()) > 0) {
            return new SizeCheckResult(false, 
                String.format("包裹宽度%.1fcm超过限制%.1fcm", width, restriction.getMaxWidth()), 
                "EXCEED_MAX_WIDTH");
        }

        // 检查最大高度
        if (restriction.getMaxHeight() != null && height.compareTo(restriction.getMaxHeight()) > 0) {
            return new SizeCheckResult(false, 
                String.format("包裹高度%.1fcm超过限制%.1fcm", height, restriction.getMaxHeight()), 
                "EXCEED_MAX_HEIGHT");
        }

        // 检查最大单边长度
        if (restriction.getMaxSingleSide() != null) {
            BigDecimal maxSide = length.max(width).max(height);
            if (maxSide.compareTo(restriction.getMaxSingleSide()) > 0) {
                return new SizeCheckResult(false, 
                    String.format("包裹最大边长%.1fcm超过限制%.1fcm", maxSide, restriction.getMaxSingleSide()), 
                    "EXCEED_MAX_SINGLE_SIDE");
            }
        }

        // 检查最大周长
        if (restriction.getMaxGirth() != null) {
            BigDecimal girth = length.add(width).add(height);
            if (girth.compareTo(restriction.getMaxGirth()) > 0) {
                return new SizeCheckResult(false, 
                    String.format("包裹周长%.1fcm超过限制%.1fcm", girth, restriction.getMaxGirth()), 
                    "EXCEED_MAX_GIRTH");
            }
        }

        // 检查最大体积
        if (restriction.getMaxVolume() != null) {
            BigDecimal volume = length.multiply(width).multiply(height);
            if (volume.compareTo(restriction.getMaxVolume()) > 0) {
                return new SizeCheckResult(false, 
                    String.format("包裹体积%.1fcm³超过限制%.1fcm³", volume, restriction.getMaxVolume()), 
                    "EXCEED_MAX_VOLUME");
            }
        }

        return new SizeCheckResult(true, null, null);
    }

    /**
     * 验证尺寸限制配置格式是否正确
     *
     * @param sizeRestrictionsJson JSON格式的尺寸限制配置
     * @return 是否有效
     */
    public static boolean isValidSizeRestrictions(String sizeRestrictionsJson) {
        if (StrUtil.isBlank(sizeRestrictionsJson)) {
            return true; // 空配置认为是有效的
        }

        try {
            SizeRestriction restriction = parseSizeRestrictions(sizeRestrictionsJson);
            return restriction != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成尺寸限制的可读描述
     *
     * @param sizeRestrictionsJson JSON格式的尺寸限制配置
     * @return 可读描述
     */
    public static String generateSizeDescription(String sizeRestrictionsJson) {
        SizeRestriction restriction = parseSizeRestrictions(sizeRestrictionsJson);
        if (restriction == null) {
            return "无尺寸限制";
        }

        StringBuilder sb = new StringBuilder();
        
        if (restriction.getMaxLength() != null || restriction.getMaxWidth() != null || restriction.getMaxHeight() != null) {
            sb.append("最大尺寸: ");
            if (restriction.getMaxLength() != null) {
                sb.append("长").append(restriction.getMaxLength()).append("cm ");
            }
            if (restriction.getMaxWidth() != null) {
                sb.append("宽").append(restriction.getMaxWidth()).append("cm ");
            }
            if (restriction.getMaxHeight() != null) {
                sb.append("高").append(restriction.getMaxHeight()).append("cm ");
            }
        }
        
        if (restriction.getMaxSingleSide() != null) {
            if (sb.length() > 0) sb.append("; ");
            sb.append("最大单边: ").append(restriction.getMaxSingleSide()).append("cm");
        }
        
        if (restriction.getMaxGirth() != null) {
            if (sb.length() > 0) sb.append("; ");
            sb.append("最大周长: ").append(restriction.getMaxGirth()).append("cm");
        }
        
        if (restriction.getMaxVolume() != null) {
            if (sb.length() > 0) sb.append("; ");
            sb.append("最大体积: ").append(restriction.getMaxVolume()).append("cm³");
        }
        
        return sb.length() > 0 ? sb.toString() : "无尺寸限制";
    }

    /**
     * 解析新格式的尺寸限制规则
     *
     * @param sizeRestrictionsJson JSON格式的尺寸限制配置
     * @return 尺寸限制规则对象
     */
    public static SizeLimitRule parseSizeLimitRule(String sizeRestrictionsJson) {
        if (StrUtil.isBlank(sizeRestrictionsJson)) {
            return null;
        }

        try {
            JSONObject json = JSONUtil.parseObj(sizeRestrictionsJson);
            SizeLimitRule rule = new SizeLimitRule();

            // 最小尺寸
            if (json.containsKey("min_length_cm")) {
                rule.setMinLength(json.getInt("min_length_cm"));
            }
            if (json.containsKey("min_width_cm")) {
                rule.setMinWidth(json.getInt("min_width_cm"));
            }
            if (json.containsKey("min_height_cm")) {
                rule.setMinHeight(json.getInt("min_height_cm"));
            }

            // 最大尺寸
            if (json.containsKey("max_length_cm")) {
                rule.setMaxLength(json.getInt("max_length_cm"));
            }
            if (json.containsKey("max_width_cm")) {
                rule.setMaxWidth(json.getInt("max_width_cm"));
            }
            if (json.containsKey("max_height_cm")) {
                rule.setMaxHeight(json.getInt("max_height_cm"));
            }

            // 复杂规则
            if (json.containsKey("max_single_side_cm")) {
                rule.setMaxSingleSide(json.getInt("max_single_side_cm"));
            }
            if (json.containsKey("max_total_dimension_cm")) {
                rule.setMaxTotalDimension(json.getInt("max_total_dimension_cm"));
            }
            if (json.containsKey("max_length_plus_double_girth_cm")) {
                rule.setMaxLengthPlusDoubleGirth(json.getInt("max_length_plus_double_girth_cm"));
            }
            if (json.containsKey("max_second_longest_side_cm")) {
                rule.setMaxSecondLongestSide(json.getInt("max_second_longest_side_cm"));
            }

            // 超尺寸处理
            if (json.containsKey("oversize_fee_cny")) {
                rule.setOversizeFee(json.getInt("oversize_fee_cny"));
            }
            if (json.containsKey("oversize_inquiry_required")) {
                rule.setOversizeInquiryRequired(json.getBool("oversize_inquiry_required"));
            }

            return rule;
        } catch (Exception e) {
            log.error("解析新格式尺寸限制配置失败: {}", sizeRestrictionsJson, e);
            return null;
        }
    }

    /**
     * 使用新的校验器检查尺寸
     *
     * @param length 长度(cm)
     * @param width 宽度(cm)
     * @param height 高度(cm)
     * @param sizeRestrictionsJson 尺寸限制配置JSON
     * @return 检查结果
     */
    public static SizeCheckResult checkSizeWithNewValidator(BigDecimal length, BigDecimal width, BigDecimal height,
                                                           String sizeRestrictionsJson) {
        SizeLimitRule rule = parseSizeLimitRule(sizeRestrictionsJson);
        if (rule == null) {
            // 尝试使用旧格式
            return checkSize(length, width, height, sizeRestrictionsJson);
        }

        SizeValidator.ValidationResult result = SizeValidator.validate(rule, length, width, height);
        return new SizeCheckResult(result.isValid(), result.getErrorMessage(),
                                 result.isValid() ? null : "SIZE_RESTRICTION");
    }
}
