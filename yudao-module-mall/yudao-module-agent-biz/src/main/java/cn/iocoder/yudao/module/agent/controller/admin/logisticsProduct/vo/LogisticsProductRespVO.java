package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 代购物流公司产品 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LogisticsProductRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11367")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "物流公司编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28989")
    @ExcelProperty("物流公司编号")
    private Long companyId;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "渠道编码")
    @ExcelProperty("渠道编码")
    private String channelCode;

    @Schema(description = "中文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中文名称")
    private String nameZh;

    @Schema(description = "英文名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("英文名称")
    private String nameEn;

    /**
     * 法语名称
     */
    private String nameFr;
    /**
     * 德语名称
     */
    private String nameDe;
    /**
     * 西班牙语名称
     */
    private String nameEs;
    /**
     * 阿拉伯语名称
     */
    private String nameAr;
    /**
     * 中文特性描述
     */
    private String featuresZh;
    /**
     * 英文特性描述
     */
    private String featuresEn;
    /**
     * 法语特性描述
     */
    private String featuresFr;
    /**
     * 德语特性描述
     */
    private String featuresDe;
    /**
     * 西班牙语特性描述
     */
    private String featuresEs;
    /**
     * 阿拉伯语特性描述
     */
    private String featuresAr;
    /**
     * 产品图标
     */
    private String iconUrl;
    /**
     * 是否包税
     *
     */
    private Boolean taxInclude;
    /**
     * 是否计算体积重
     *
     */
    private Boolean needVolumeCal;
    /**
     * 体积重计算基数
     */
    private Integer volumeBase;
    /**
     * 最小重量(g)
     */
    private Integer minWeight;
    /**
     * 最大重量(g)
     */
    private Integer maxWeight;
    /**
     * 中文尺寸限制描述
     */
    private String dimensionRestrictionZh;
    /**
     * 英文尺寸限制描述
     */
    private String dimensionRestrictionEn;
    /**
     * 法语尺寸限制描述
     */
    private String dimensionRestrictionFr;
    /**
     * 德语尺寸限制描述
     */
    private String dimensionRestrictionDe;
    /**
     * 西班牙尺寸限制描述
     */
    private String dimensionRestrictionEs;
    /**
     * 阿拉伯尺寸限制描述
     */
    private String dimensionRestrictionAr;
    /**
     * 中文体积重量计费规则描述
     */
    private String volumeWeightRuleZh;
    /**
     * 英文体积重量计费规则描述
     */
    private String volumeWeightRuleEn;
    /**
     * 法语体积重量计费规则描述
     */
    private String volumeWeightRuleFr;
    /**
     * 德语体积重量计费规则描述
     */
    private String volumeWeightRuleDe;
    /**
     * 西班牙体积重量计费规则描述
     */
    private String volumeWeightRuleEs;
    /**
     * 阿拉伯体积重量计费规则描述
     */
    private String volumeWeightRuleAr;
    /**
     * 最小申报价值(分)
     */
    private Integer minDeclareValue;
    /**
     * 最大申报价值(分)
     */
    private Integer maxDeclareValue;
    //妥投率
    private BigDecimal deliveryRate;
    /**
     * 默认申报类型
     */
    private String defaultDeclareType;
    /**
     * 每公斤申报价值(分)
     */
    private BigDecimal declarePerKg;
    /**
     * 申报比例
     */
    private BigDecimal declareRatio;
    /**
     * 免费保险
     *
     */
    private Boolean freeInsure;
    /**
     * IOSS
     *
     */
    private Boolean iossEnabled;
    /**
     * 计算公式表达式
     */
    private String calculationFormula;
    /**
     * 分类限制配置
     */
    private String categoryRestrictions;

    /**
     * 服务等级 : 优先priority, 标准standard, 经济economy
     */
    private String serviceLevel;

    /**
     * 是否可带电
     *
     */
    private Boolean electronic;

    /**
     * 是否化妆品专线
     *
     */
    private Boolean cosmetic;

    /**
     * 是否服装专线
     *
     */
    private Boolean clothing;

    /**
     * 是否大货专线
     *
     */
    private Boolean large;

    /**
     * 是否推荐
     */
    private Boolean recommended;

    /**
     * 备注
     */
    private String memo;

    @Schema(description = "排序", example = "1")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "开启状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("开启状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}