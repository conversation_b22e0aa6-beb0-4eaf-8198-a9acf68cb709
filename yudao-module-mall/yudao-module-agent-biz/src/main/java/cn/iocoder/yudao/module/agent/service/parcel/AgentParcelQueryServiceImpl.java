package cn.iocoder.yudao.module.agent.service.parcel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.agent.controller.admin.parcel.vo.AgentParcelPageReqVO;
import cn.iocoder.yudao.module.agent.controller.app.base.server.AgentServeBaseVO;
import cn.iocoder.yudao.module.agent.controller.app.parcel.vo.AppAgentParcelRespVO;
import cn.iocoder.yudao.module.agent.convert.parcel.AgentParcelConvert;
import cn.iocoder.yudao.module.agent.dal.dataobject.logisticsProduct.LogisticsProductDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.parcel.AgentParcelItemDO;
import cn.iocoder.yudao.module.agent.dal.dataobject.serve.AgentServeDO;
import cn.iocoder.yudao.module.agent.dal.mysql.parcel.AgentParcelMapper;
import cn.iocoder.yudao.module.agent.dal.mysql.parcel.AgentParcelItemMapper;
import cn.iocoder.yudao.module.agent.framework.delivery.core.client.dto.ExpressTrackRespDTO;
import cn.iocoder.yudao.module.agent.service.logisticsProduct.LogisticsProductService;
import cn.iocoder.yudao.module.agent.service.serve.AgentServeService;
import cn.iocoder.yudao.module.agent.service.serve.bo.AgentServerBO;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLang;
import static cn.iocoder.yudao.framework.web.core.context.LanguageContext.getCurrentLanguage;

/**
 * @program: ruoyi-vue-pro
 * @description: 代购包裹订单读服务 实现类
 * @author: DingXiao
 * @create: 2025-04-30 15:11
 **/
@Slf4j
@Service
public class AgentParcelQueryServiceImpl implements AgentParcelQueryService{

    @Resource
    private AgentParcelMapper parcelMapper;
    @Resource
    private AgentParcelItemMapper parcelItemMapper;

    @Resource
    private AgentServeService serveService;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private LogisticsProductService logisticsProductService;

    @Override
    public AgentParcelDO getParcel(Long id) {
        return parcelMapper.selectById(id);
    }

    @Override
    public List<AgentParcelItemDO> getParcelItemListByOrderId(Long orderId) {

        return parcelItemMapper.selectListByParcelId(orderId);
    }

    @Override
    public AppAgentParcelRespVO getParcelDetail(Long id) {
        // 1.1 获取包裹订单
        AgentParcelDO parcel = parcelMapper.selectById(id);
        if (parcel == null) {
            return null;
        }

        // 1.2 获取包裹项
        List<AgentParcelItemDO> items = parcelItemMapper.selectListByParcelId(id);

        AppAgentParcelRespVO respVO = AgentParcelConvert.INSTANCE.convert(parcel, items);
        //处理收货地址
        respVO.setReceiverAreaName(AreaUtils.formatInternational(respVO.getReceiverAreaId()));

        // 2.0 处理服务项
        // 2.1 获取保险、免费和收费服务的ID集合
        List<Long> insuranceIds = Optional.ofNullable(parcel.getInsuranceServices()).orElse(Collections.emptyList());
        List<Long> freeServiceIds = Optional.ofNullable(parcel.getFreeServices()).orElse(Collections.emptyList());
        List<Long> chargeServiceIds = Optional.ofNullable(parcel.getChargeServices()).orElse(Collections.emptyList());

        Collection<Long> allIds = CollUtil.union(
                CollUtil.union(insuranceIds, freeServiceIds),
                chargeServiceIds
        );

        // 2.2 批量获取服务详情
        List<AgentServerBO> serveList = CollUtil.isEmpty(allIds)
                ? Collections.emptyList()
                : serveService.selectListByIds(allIds, getCurrentLanguage().getLanguage());




        // 2.3 构建 ID -> Service BO 的映射，提升查找效率
        Map<Long, AgentServerBO> serviceMap = serveList.stream()
                .collect(Collectors.toMap(AgentServerBO::getId, Function.identity(), (existing, replacement) -> existing));

        // 2.4  分别填充三种服务列表，同时考虑服务项可能不存在的情况
        List<AgentServeBaseVO> insuranceServices = filterByIds(serviceMap, insuranceIds);
        List<AgentServeBaseVO> freeServices = filterByIds(serviceMap, freeServiceIds);
        List<AgentServeBaseVO> chargeServices = filterByIds(serviceMap, chargeServiceIds);

        respVO.setInsuranceServices(insuranceServices);
        respVO.setFreeServices(freeServices);
        respVO.setChargeServices(chargeServices);

        LogisticsProductDO logisticsProduct = logisticsProductService.getLogisticsProduct(parcel.getTransportPlanId());
        if(logisticsProduct != null){
            respVO.setTransportPlanName(getLogisticsProductName(logisticsProduct,getCurrentLang()));
        }

        return respVO;
    }



    @Override
    public PageResult<AgentParcelDO> getParcelPage(AgentParcelPageReqVO reqVO) {
        // 根据用户查询条件构建用户编号列表
        Set<Long> userIds = buildQueryConditionUserIds(reqVO);
        if (userIds == null) { // 没查询到用户，说明肯定也没他的订单
            return PageResult.empty();
        }
        return parcelMapper.selectPage(reqVO, userIds);
    }

    @Override
    public List<AgentParcelItemDO> getParcelItemListByParcelId(Collection<Long> parcelIds) {
        if (CollUtil.isEmpty(parcelIds)) {
            return Collections.emptyList();
        }
        return parcelItemMapper.selectListByParcelId(parcelIds);
    }

    @Override
    public List<ExpressTrackRespDTO> getParcelTrackList(Long id) {
        // todo ding
        return null;
    }

    @Override
    public AgentParcelItemDO getParcelItem(Long parcelItemId) {
        return parcelItemMapper.selectById( parcelItemId);
    }

    @Override
    public AgentParcelItemDO getParcelItem(Long userId, Long itemId) {
        AgentParcelItemDO  orderItem = parcelItemMapper.selectById(itemId);
         if (orderItem != null && ObjectUtil.notEqual(orderItem.getUserId(), userId)) {
             return null;
         }
        return orderItem;
    }

    @Override
    public AgentParcelDO getParcel(Long userId, Long id) {
         AgentParcelDO parcel = parcelMapper.selectById(id);
          if (parcel != null && ObjectUtil.notEqual(parcel.getUserId(), userId)) {
             return null;
         }
        return parcel;
    }

    /**
     * 根据 ID 列表从 Map 中筛选出对应的服务对象列表
     */
    private List<AgentServeBaseVO> filterByIds(Map<Long, AgentServerBO> serviceMap, List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return ids.stream()
                .map(serviceMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private Set<Long> buildQueryConditionUserIds(AgentParcelPageReqVO reqVO) {
        // 获得 userId 相关的查询
        Set<Long> userIds = new HashSet<>();
        if (StrUtil.isNotEmpty(reqVO.getUserMobile())) {
            MemberUserRespDTO user = memberUserApi.getUserByMobile(reqVO.getUserMobile());
            if (user == null) { // 没查询到用户，说明肯定也没他的订单
                return null;
            }
            userIds.add(user.getId());
        }
        if (StrUtil.isNotEmpty(reqVO.getUserNickname())) {
            List<MemberUserRespDTO> users = memberUserApi.getUserListByNickname(reqVO.getUserNickname());
            if (CollUtil.isEmpty(users)) { // 没查询到用户，说明肯定也没他的订单
                return null;
            }
            userIds.addAll(convertSet(users, MemberUserRespDTO::getId));
        }
        return userIds;
    }

    private String getLogisticsProductName(LogisticsProductDO logisticsProduct, String currentLang) {
        switch (currentLang) {
            case "zh":
                return logisticsProduct.getNameZh();
            case "fr":
                return logisticsProduct.getNameFr();
            case "de":
                return logisticsProduct.getNameDe();
            case "es":
                return logisticsProduct.getNameEs();
            case "ar":
                return logisticsProduct.getNameAr();
            case "en":
            default:
                return logisticsProduct.getNameEn();
        }
    }


}
