package cn.iocoder.yudao.module.agent.controller.admin.logisticsProduct.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代购物流产品价格规则 Excel 导入 VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class LogisticsProductPriceImportExcelVO {

    @ExcelProperty("国家编码")
    private String countryCode;

    @ExcelProperty("分区编号")
    private String zoneCode;

    @ExcelProperty("时效")
    private String transitTime;

    @ExcelProperty("最小重量(g)")
    private Integer minWeight;

    @ExcelProperty("最大重量(g)")
    private Integer maxWeight;

    @ExcelProperty("价格配置JSON")
    @Schema(description = "价格配置(JSON格式)，根据priceType区分：TIERED使用TieredPriceItem数组，TIERED_INCREMENTAL使用TieredIncrementalItem数组",
            example = "[{\"tierStart\":0,\"tierEnd\":500,\"unitPrice\":12000,\"registrationFee\":500,\"roundingUnit\":100,\"minChargeWeight\":100}]")
    private String priceConfig;


    @ExcelProperty("计费方式")
    private String chargeType;

    @ExcelProperty("价格类型")
    @Schema(description = "价格类型", example = "INCREMENTAL", allowableValues = {"INCREMENTAL", "TIERED", "TIERED_INCREMENTAL"})
    private String priceType;

    /**
     * 最小申报价值(分)
     */
    @ExcelProperty("最小申报价值(分)")
    @Schema(description = "最小申报价值(分)", example = "100")
    private Integer minDeclareValue;
    /**
     * 最大申报价值(分)
     */
    @ExcelProperty("最大申报价值(分)")
    @Schema(description = "最大申报价值(分)", example = "100")
    private Integer maxDeclareValue;

    /**
     * 妥投率
     */
    @ExcelProperty("妥投率")
    @Schema(description = "妥投率", example = "0.9")
    private BigDecimal deliveryRate;


    @ExcelProperty("首重(g)/首件数量")
    private Integer firstUnit;

    @ExcelProperty("首重/首件价格(分)")
    private Integer firstPrice;

    @ExcelProperty("续重(g)/续件单位")
    private Integer additionalUnit;

    @ExcelProperty("续重/续件价格(分)")
    private Integer additionalPrice;



    @ExcelProperty("燃油费率")
    private BigDecimal fuelFeeRate;

    @ExcelProperty("挂号费(分)")
    private Integer registrationFee;

    @ExcelProperty("操作费(分)")
    private Integer operationFee;

    @ExcelProperty("服务费(分)")
    private Integer serviceFee;

    @ExcelProperty("清关费(分)")
    private Integer customsFee;

    @ExcelProperty("是否预收关税")
    private Boolean prepayTariff;

    @ExcelProperty("关税税率")
    private BigDecimal tariffRate;

    @ExcelProperty("尺寸限制配置JSON")
    private String sizeRestrictions;

    @ExcelProperty("生效日期")
    private LocalDateTime effectiveTime;

    @ExcelProperty("失效日期")
    private LocalDateTime expireTime;
    /**
     * 折扣费率
     */
    @ExcelProperty("折扣费率")
    @Schema(description = "折扣费率", example = "1.5")
    private BigDecimal discountRate;


}
