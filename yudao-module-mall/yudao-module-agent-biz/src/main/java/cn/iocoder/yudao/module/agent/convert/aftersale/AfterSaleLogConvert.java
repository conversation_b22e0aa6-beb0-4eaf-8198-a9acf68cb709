package cn.iocoder.yudao.module.agent.convert.aftersale;


import cn.iocoder.yudao.module.agent.dal.dataobject.aftersale.AgentAfterSaleLogDO;
import cn.iocoder.yudao.module.agent.service.aftersale.bo.AfterSaleLogCreateReqBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AfterSaleLogConvert {

    AfterSaleLogConvert INSTANCE = Mappers.getMapper(AfterSaleLogConvert.class);

    AgentAfterSaleLogDO convert(AfterSaleLogCreateReqBO bean);

}
