package cn.iocoder.yudao.module.product.enums.spu;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @program: ruoyi-vue-pro
 * @description: 商品来源类型
 * @author: DingXiao
 * @create: 2025-07-01 10:11
 **/
@Getter
@AllArgsConstructor
public enum ProductSourceTypeEnum implements IntArrayValuable {
    SELF(0, "自营"),
    AGENT(1, "代购"),
    CUSTOM(2,"自定义" );

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ProductSourceTypeEnum::getType).toArray();
    /**
     * 类型
     */
    private final Integer type;
    /**
     * 状态名
     */
    private final String name;


    @Override
    public int[] array() {
        return new int[0];
    }

    /**
     * 判断是否处于【代购】类型
     *
     * @param type 类型
     * @return 是否【代购】类型
     */
    public static boolean isAgent(Integer type) {
        return AGENT.getType().equals(type);
    }
}
