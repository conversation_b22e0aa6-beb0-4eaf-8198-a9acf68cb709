# 商品分类相关
product.category.not_exists=Category does not exist
product.category.parent_not_exists=Parent category does not exist
product.category.parent_not_first_level=The parent category cannot be a second-level category
product.category.exists_children=Cannot delete because subcategories exist
product.category.disabled=Category ({}) is disabled and cannot be used
product.category.have_bind_spu=Cannot delete because products exist under the category

# 商品品牌相关
product.brand.not_exists=Brand does not exist
product.brand.disabled=Brand is disabled
product.brand.name_exists=Brand name already exists

# 商品属性项
product.property.not_exists=Property item does not exist
product.property.exists=The name of the property item already exists
product.property.delete_fail_value_exists=Cannot delete because property values exist under the property item

# 商品属性值
product.property_value.not_exists=Property value does not exist
product.property_value.exists=The name of the property value already exists

# 商品 SPU
product.spu.not_exists=Product SPU does not exist
product.spu.save_fail_category_level_error=Incorrect product category: Only secondary-level or lower categories can be used
product.spu.save_fail_coupon_template_not_exists=Failed to save product SPU: Coupon does not exist
product.spu.not_enable=Product SPU [{}] is not in an active (available) state
product.spu.not_recycle=Product SPU is not in the recycle bin state
product.spu.already_exists=Product SPU [{}] already exists

# 商品 SKU
product.sku.not_exists=Product SKU does not exist
product.sku.properties_duplicated=The property combination of the product SKU is duplicated
product.spu.attr_numbers_must_be_equals=The attributes of each SKU under the same SPU must be consistent
product.spu.sku_not_duplicate=Each SKU under the same SPU must be unique
product.sku.stock_not_enough=Product SKU stock is insufficient

# 商品评价
product.comment.not_exists=Product review does not exist
product.comment.order_exists=A product review for the order already exists

# 商品收藏
product.favorite.exists=The product has already been favorited
product.favorite.not_exists=Product favorite does not exist

# 爬虫搜索
product.search.failed=Product search failed:{}
product.search.detail.failed= Product detail search failed:{}
product.search.system.error= Product search system error:{}
product.search.config.error= Product search configuration error:{}
product.search.config.not_found= Product search configuration does not exist
product.search.config.invalid= Product search configuration is invalid
product.search.platform.not_support= Product search platform is not supported
