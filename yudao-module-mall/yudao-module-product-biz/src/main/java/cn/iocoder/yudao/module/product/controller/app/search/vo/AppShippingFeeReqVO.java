package cn.iocoder.yudao.module.product.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户 App - 获取商品运费的 Request VO
 *
 * <AUTHOR>
 */

@Schema(description = "用户 App - 获取商品运费的 Request VO")
@Data
public class AppShippingFeeReqVO {

    @Schema(description = "平台代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "taobao")
    private String platform;

    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String sourceId;

    @Schema(description = "地区ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "320205")
    private String areaId;

    @Schema(description = "语言代码", example = "zh")
    private String lang;
}
