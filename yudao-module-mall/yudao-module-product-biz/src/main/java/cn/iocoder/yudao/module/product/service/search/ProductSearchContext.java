package cn.iocoder.yudao.module.product.service.search;


import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.product.controller.app.search.vo.*;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.product.service.history.ProductBrowseHistoryService;
import cn.iocoder.yudao.module.product.service.search.dto.SearchProductDetailReqDTO;
import cn.iocoder.yudao.module.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.product.service.spu.ProductSpuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Component
@Slf4j
public class ProductSearchContext {

    /**
     * 商品搜索策略配置键
     */
    static final String PRODUCT_SEARCH_STRATEGY_KEY = "product.search.strategy";

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private ConfigApi configApi;

    @Resource
    @Lazy
    private ProductSpuService productSpuService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private ProductBrowseHistoryService productBrowseHistoryService;

    private ProductSearchService productSearchService;

    @PostConstruct
    public void init() {
        // 从系统配置中获取当前使用的搜索策略
        String strategyType = configApi.getConfigValueByKey(PRODUCT_SEARCH_STRATEGY_KEY);
        if (strategyType == null) {
            strategyType = "oneBound"; // 默认使用爬虫策略
            log.warn("[init] 未配置商品搜索策略，使用默认策略: {}", strategyType);
        }
        setProductSearchService(strategyType);
        log.info("[init] 商品搜索策略初始化完成，当前策略: {}", strategyType);
    }

    public void setProductSearchService(ProductSearchService productSearchService) {
        this.productSearchService = productSearchService;
    }

    public void setProductSearchService(String strategyType) {
        switch (strategyType) {
            case "crawler":
                this.productSearchService = applicationContext.getBean(CrawlerServiceImpl.class);
                break;
            case "oneBound":
                this.productSearchService = applicationContext.getBean(OneBoundSearchServiceImpl.class);
                break;
            default:
                log.error("[setProductSearchService] 未知的策略类型: {}", strategyType);
                throw new IllegalArgumentException("Unknown strategy type: " + strategyType);
        }
        log.info("[setProductSearchService] 切换商品搜索策略为: {}", strategyType);
    }


    /**
     * 搜索商品
     *
     * @param reqVO 搜索请求
     * @return 搜索结果
     */
    public PageResult<AppProductSpuRespVO> searchProducts(AppSearchPageReqVO reqVO){
        return productSearchService.searchProducts(reqVO);
    }


    /**
     * 搜索商品图片
     *
     * @param reqVO 搜索请求
     * @return 搜索结果
     */
    public PageResult<AppProductSpuRespVO> searchByImg(AppSearchImgPageReqVO reqVO){
        return productSearchService.searchByImg(reqVO);
    }

    /**
     * 根据商品链接获取详情
     *
     * @param url 商品链接
     * @return 商品详情
     */
    public AppProductSpuDetailRespVO getProductByUrl(String url,String lang){

        //先查询本地是否已经存在此商品
        SearchProductDetailReqDTO requestDTO = parseProductUrl(url,lang);

        ProductSpuDO spu = productSpuService.getAgentSpu(requestDTO.getSourceId(), requestDTO.getSource(), lang);
        //商品已存在本地库 直接返回
        if(spu != null){
            log.info("[getProductDetail] 商品已存在，直接返回");
            // 获得商品 SKU
            List<ProductSkuDO> skus = productSkuService.getSkuListBySpuId(spu.getId());
            // 增加浏览量
            productSpuService.updateBrowseCount(spu.getId(), 1);
            // 保存浏览记录 ding 加入缓存功能后 浏览记录需要以谷歌统计为准
            productBrowseHistoryService.createBrowseHistory(getLoginUserId(), spu.getId());

            // 拼接返回
            spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount());
            AppProductSpuDetailRespVO spuVO = BeanUtils.toBean(spu, AppProductSpuDetailRespVO.class)
                    .setSkus(BeanUtils.toBean(skus, AppProductSpuDetailRespVO.Sku.class));
            return spuVO;
        }
        log.info("[getProductByUrl] 商品不存在，开始获取商品信息");
        return productSearchService.getProductByUrl(url, lang);
    }

    /**
     * 根据商品ID获取商品详情
     *
     * @param reqVO 详情请求
     * @return 详情
     */
    public AppProductSpuDetailRespVO getProductDetailById(AppSearchDetailReqVO reqVO){
        ProductSpuDO spu = productSpuService.getAgentSpu(reqVO.getId(), reqVO.getPlatform(), reqVO.getLang());
        //商品已存在本地库 直接返回
        if(spu != null){
            log.info("[getProductDetailById] 商品已存在，直接返回");
            // 获得商品 SKU
            List<ProductSkuDO> skus = productSkuService.getSkuListBySpuId(spu.getId());
            // 增加浏览量
            productSpuService.updateBrowseCount(spu.getId(), 1);
            // 保存浏览记录 ding 加入缓存功能后 浏览记录需要以谷歌统计为准
            productBrowseHistoryService.createBrowseHistory(getLoginUserId(), spu.getId());

            // 拼接返回
            spu.setSalesCount(spu.getSalesCount() + spu.getVirtualSalesCount());
            AppProductSpuDetailRespVO spuVO = BeanUtils.toBean(spu, AppProductSpuDetailRespVO.class)
                    .setSkus(BeanUtils.toBean(skus, AppProductSpuDetailRespVO.Sku.class));
            return spuVO;
        }

        log.info("[getProductDetailById] 商品不存在，开始获取商品信息");

        return productSearchService.getProductDetailById(reqVO);
    }

    /**
     * 根据商品ID获取商品详情
     *
     * @param source 来源
     * @param sourceId 来源商品ID
     * @param lang 语言
     * @return 详情
     */
    public AppProductSpuDetailRespVO getProductDetailBySourceId(String sourceId, String source,String lang){
        return productSearchService.getProductDetailBySourceId(sourceId, source, lang);
    }

    /**
     * 获取商品运费
     *
     * @param reqVO 运费请求
     * @return 运费
     */
    public Integer getShippingFee(@Valid AppShippingFeeReqVO reqVO) {
        return productSearchService.getShippingFee(reqVO);
    }

    /**
     * 获取商品描述
     *
     * @param reqVO 描述请求
     * @return 描述
     */
    public String getProductDesc(@Valid AppProductDescReqVO reqVO) {
        return productSearchService.getProductDesc(reqVO);
    }


    /**
     * 解析商品URL获取商品ID和平台信息
     */
    public SearchProductDetailReqDTO parseProductUrl(String url,String lang) {
        if (StrUtil.isBlank(url)) {
            return null;
        }

        // 淘宝天猫URL解析
        if (url.contains("taobao.com") || url.contains("tmall.com")) {
            Pattern pattern = Pattern.compile("id=(\\d+)");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "taobao", lang);
            }
        }

        // 1688URL解析
        if (url.contains("1688.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "1688", lang);
            }
        }

        // 京东URL解析
        if (url.contains("jd.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "jd", lang);
            }
        }
        // 唯品会URL解析
        if (url.contains("vip.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "vip", lang);
            }
        }
        // 微店URL解析
        if (url.contains("vip.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "weidian" , lang);
            }
        }
        // 拼多多URL解析
        if (url.contains("pinduoduo.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new SearchProductDetailReqDTO(matcher.group(1), "pinduoduo" , lang);
            }
        }

        return null;
    }

}
