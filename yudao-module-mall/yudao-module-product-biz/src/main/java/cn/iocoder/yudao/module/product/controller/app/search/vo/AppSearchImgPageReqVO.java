package cn.iocoder.yudao.module.product.controller.app.search.vo;


import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Schema(description = "用户 App - 图片商品搜索分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSearchImgPageReqVO extends PageParam {

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.png")
    private String imgId;

//    @Schema(description = "图片类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    private Integer img_type=0;

    @Schema(description = "平台代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "taobao")
    @NotBlank(message = "平台代码不能为空")
    private String platform;

    @Schema(description = "语言代码", example = "zh")
    private String lang = "zh";
}
