package cn.iocoder.yudao.module.product.framework.onebound.dto.deserializer;

import cn.iocoder.yudao.module.product.framework.onebound.dto.OneBoundDetailRespDTO;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * 灵活的Video反序列化器
 * 处理空数组、null、对象等多种情况
 *
 * <AUTHOR>
 */
public class FlexibleVideoDeserializer extends JsonDeserializer<OneBoundDetailRespDTO.Video> {

    @Override
    public OneBoundDetailRespDTO.Video deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken token = p.getCurrentToken();
        
        // 处理null值
        if (token == JsonToken.VALUE_NULL) {
            return null;
        }
        
        // 处理空字符串
        if (token == JsonToken.VALUE_STRING) {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty()) {
                return null;
            }
            // 如果是非空字符串，创建Video对象
            OneBoundDetailRespDTO.Video video = new OneBoundDetailRespDTO.Video();
            video.setUrl(value);
            return video;
        }
        
        // 处理空数组
        if (token == JsonToken.START_ARRAY) {
            // 跳过数组内容
            while (p.nextToken() != JsonToken.END_ARRAY) {
                // 空循环，只是跳过数组内容
            }
            return null;
        }
        
        // 处理对象
        if (token == JsonToken.START_OBJECT) {
            OneBoundDetailRespDTO.Video video = new OneBoundDetailRespDTO.Video();
            while (p.nextToken() != JsonToken.END_OBJECT) {
                String fieldName = p.getCurrentName();
                p.nextToken();
                if ("url".equals(fieldName)) {
                    video.setUrl(p.getValueAsString());
                }
            }
            return video;
        }
        
        // 其他情况返回null
        return null;
    }
}
