package cn.iocoder.yudao.module.product.service.search;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.product.controller.app.search.vo.*;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuDetailRespVO;
import cn.iocoder.yudao.module.product.controller.app.spu.vo.AppProductSpuRespVO;
import cn.iocoder.yudao.module.product.enums.search.OneBoundPlatformEnum;
import cn.iocoder.yudao.module.product.framework.onebound.convert.OneBoundConvert;
import cn.iocoder.yudao.module.product.framework.onebound.core.OneBoundClient;
import cn.iocoder.yudao.module.product.framework.onebound.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.interceptor.SimpleKey;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantId;
import static cn.iocoder.yudao.module.product.dal.redis.RedisKeyConstants.PRODUCT_SEARCH_DETAIL;
import static cn.iocoder.yudao.module.product.dal.redis.RedisKeyConstants.PRODUCT_SEARCH_KEYWORD;
import static cn.iocoder.yudao.module.product.enums.ErrorCodeConstants.ONEBOUND_DETAIL_FAILED;

/**
 * OneBound商品搜索服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OneBoundSearchServiceImpl implements ProductSearchService {

    @Resource
    private OneBoundClient oneBoundClient;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Cacheable(cacheNames = PRODUCT_SEARCH_KEYWORD ,
            key = "{#reqVO.platform, #reqVO.keyword,#reqVO.lang, #reqVO.pageNo, #reqVO.pageSize, #reqVO.sort}",
            unless = "#result == null || #result.list == null || #result.list.isEmpty()")
    public PageResult<AppProductSpuRespVO> searchProducts(AppSearchPageReqVO reqVO) {
        log.info("[searchProducts] 开始处理OneBound搜索请求，参数: {}", JsonUtils.toJsonString(reqVO));

        try {
            // 转换请求参数
            OneBoundSearchReqDTO searchReq = OneBoundConvert.INSTANCE.convertSearchReq(reqVO);

            // 根据平台调用不同的API
            OneBoundPlatformEnum platform = OneBoundPlatformEnum.getByCode(reqVO.getPlatform());
            if (platform == null) {
                log.warn("[searchProducts] 不支持的平台: {}", reqVO.getPlatform());
                return new PageResult<>(new ArrayList<>(), 0L);
            }

            OneBoundSearchRespDTO respDTO = oneBoundClient.searchProducts(platform, searchReq);

            // 验证响应数据
            if (respDTO == null || respDTO.getItems() == null || respDTO.getItems().getItem() == null) {
                log.warn("[searchProducts] OneBound返回空数据，平台: {}", reqVO.getPlatform());
                return new PageResult<>(new ArrayList<>(), 0L);
            }

            // 转换响应数据
            PageResult<AppProductSpuRespVO> result = OneBoundConvert.INSTANCE.convertSearchResp(respDTO, reqVO.getPlatform());
            log.info("[searchProducts] OneBound搜索完成，返回结果: 总数={}, 当前页数据量={}",
                    result.getTotal(), result.getList() != null ? result.getList().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("[searchProducts] OneBound搜索异常，平台: {}, 异常类型: {}, 异常信息: {}",
                    reqVO.getPlatform(), e.getClass().getSimpleName(), e.getMessage(), e);
            // 返回空结果而不是抛出异常，避免前端报错
            return new PageResult<>(new ArrayList<>(), 0L);
        }
    }


    @Override
    public PageResult<AppProductSpuRespVO> searchByImg(AppSearchImgPageReqVO reqVO) {

        log.info("[searchByImg] 搜索图片商品，参数: {}", JsonUtils.toJsonString(reqVO));
        try {
            // 转换请求参数
            OneBoundImageSearchReqDTO searchReq = OneBoundConvert.INSTANCE.convertImgSearchReq(reqVO);

            // 根据平台调用不同的API
            OneBoundPlatformEnum platform = OneBoundPlatformEnum.getByCode(reqVO.getPlatform());
            if (platform == null) {
                log.warn("[searchProducts] 不支持的平台: {}", reqVO.getPlatform());
                return new PageResult<>(new ArrayList<>(), 0L);
            }

            OneBoundSearchRespDTO respDTO = oneBoundClient.searchByImage(platform, searchReq);

            // 验证响应数据
            if (respDTO == null || respDTO.getItems() == null || respDTO.getItems().getItem() == null) {
                log.warn("[searchProducts] OneBound返回空数据，平台: {}", reqVO.getPlatform());
                return new PageResult<>(new ArrayList<>(), 0L);
            }

            // 转换响应数据
            PageResult<AppProductSpuRespVO> result = OneBoundConvert.INSTANCE.convertSearchResp(respDTO, reqVO.getPlatform());
            log.info("[searchProducts] OneBound搜索完成，返回结果: 总数={}, 当前页数据量={}",
                    result.getTotal(), result.getList() != null ? result.getList().size() : 0);

            return result;

        } catch (Exception e) {
            log.error("[searchProducts] OneBound搜索异常，平台: {}, 异常类型: {}, 异常信息: {}",
                    reqVO.getPlatform(), e.getClass().getSimpleName(), e.getMessage(), e);
            // 返回空结果而不是抛出异常，避免前端报错
            return new PageResult<>(new ArrayList<>(), 0L);
        }
    }

    @Override
    public AppProductSpuDetailRespVO getProductByUrl(String url,String lang) {
        log.info("[getProductByUrl] 根据URL获取商品详情，URL: {}", url);

        try {
            // 解析URL获取商品ID和平台
            ProductUrlInfo urlInfo = parseProductUrl(url);
            if (urlInfo == null) {
                throw exception(ONEBOUND_DETAIL_FAILED);
            }

            return getSelf().getProductDetailBySourceId(urlInfo.getProductId(), urlInfo.getPlatform(), lang);

        } catch (Exception e) {
            log.error("[getProductByUrl] 根据URL获取商品详情异常", e);
            throw exception(ONEBOUND_DETAIL_FAILED, e.getMessage());
        }
    }

    @Override
    public AppProductSpuDetailRespVO getProductDetailById(AppSearchDetailReqVO reqVO) {
        log.info("[getProductDetailById] 根据ID获取商品详情，请求参数: {}", JsonUtils.toJsonString(reqVO));

        return getSelf().getProductDetailBySourceId(reqVO.getId(), reqVO.getPlatform(), reqVO.getLang());
    }

    /**
     * 获取商品详情
     * 调用此方法需要使用getSelf()方法才能触发缓存
     * @param sourceId 商品ID
     * @param source   商品来源
     * @param lang     语言
     * @return 商品详情
     */
    @Override
    @Cacheable(cacheNames = PRODUCT_SEARCH_DETAIL , key = "{#source,#sourceId,#lang}", unless = "#result == null ")
    public AppProductSpuDetailRespVO getProductDetailBySourceId(String sourceId, String source, String lang) {
        log.info("[getProductDetailBySourceId] 根据来源ID获取商品详情，sourceId: {}, source: {}, lang: {}", sourceId, source, lang);

        // 构建请求参数
        OneBoundDetailReqDTO detailReq = OneBoundConvert.INSTANCE.convertDetailReq(sourceId, getApiInvokeLanguage(lang));

        // 根据平台调用不同的API
        OneBoundDetailRespDTO respDTO = oneBoundClient.getProductDetail(OneBoundPlatformEnum.getByCode(source), detailReq);

        // 验证响应数据
        if (respDTO == null || respDTO.getItem() == null) {
            log.warn("[getProductDetailBySourceId] OneBound返回空数据，商品ID: {}", sourceId);
            throw exception(ONEBOUND_DETAIL_FAILED, "商品详情获取失败，数据为空");
        }

        // 转换响应数据
        AppProductSpuDetailRespVO result = OneBoundConvert.INSTANCE.convertProductDetail(respDTO, source, lang);
        log.info("[getProductDetailBySourceId] OneBound获取详情完成，商品ID: {}", sourceId);

        return result;
    }

    @Override
    public Integer getShippingFee(AppShippingFeeReqVO reqVO) {
        // 根据平台调用不同的API
        OneBoundPlatformEnum platform = OneBoundPlatformEnum.getByCode(reqVO.getPlatform());
        if (platform == null) {
            log.warn("[getShippingFee] 不支持的平台: {}", reqVO.getPlatform());
            return 0;
        }
        OneBoundShippingFeeReqDTO reqDTO = OneBoundConvert.INSTANCE.convertShippingFeeReq(reqVO);

        OneBoundShippingFeeRespDTO oneBoundShippingFeeRespDTO = oneBoundClient.getShippingFee(platform, reqDTO);
        if(oneBoundShippingFeeRespDTO!= null && oneBoundShippingFeeRespDTO.getItem() != null && oneBoundShippingFeeRespDTO.getItem().getPostFee() != null){
            Integer fee = Integer.valueOf(oneBoundShippingFeeRespDTO.getItem().getPostFee()) * 100;//元转分
            log.info("[getShippingFee] {}API调用成功，返回商品(),运费: {}", reqVO.getPlatform(), reqVO.getSourceId(), fee);
            //填充缓存商品运费
            getSelf().fillProductShippingFee(reqVO.getPlatform(), reqVO.getSourceId(), reqVO.getLang(), fee);

            return fee;
        }

        return 0;
    }

    @Override
    public String getProductDesc(AppProductDescReqVO reqVO) {
        // 根据平台调用不同的API
        OneBoundPlatformEnum platform = OneBoundPlatformEnum.getByCode(reqVO.getPlatform());
        if (platform == null) {
            log.warn("[searchProducts] 不支持的平台: {}", reqVO.getPlatform());
            return "";
        }

        OneBoundDescReqDTO reqDTO = OneBoundConvert.INSTANCE.convertDescReq(reqVO);
        OneBoundDescRespDTO respDTO = oneBoundClient.getProductDesc(platform, reqDTO);
        if(respDTO != null && respDTO.getItem() != null && respDTO.getItem().getDesc() != null){
            log.info("[getProductDesc] {}API调用成功，返回商品描述: {}", platform.getName(), respDTO.getItem() != null && respDTO.getItem().getDesc()!= null ? respDTO.getItem().getDesc() : "null");
            //这里需要把商品描述信息增加到商品详情的缓存中去
            getSelf().fillProductDescCache(reqVO.getPlatform(), reqDTO.getNumIid(), reqDTO.getLang(), respDTO.getItem().getDesc());
            //更新数据库中商品描述信息 todo

            return respDTO.getItem().getDesc();
        }
        return "";
    }

    /**
     * 解析商品URL获取商品ID和平台信息
     */
    private ProductUrlInfo parseProductUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return null;
        }

        // 淘宝天猫URL解析
        if (url.contains("taobao.com") || url.contains("tmall.com")) {
            Pattern pattern = Pattern.compile("id=(\\d+)");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "taobao");
            }
        }

        // 1688URL解析
        if (url.contains("1688.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "1688");
            }
        }

        // 京东URL解析
        if (url.contains("jd.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "jd");
            }
        }
        // 唯品会URL解析
        if (url.contains("vip.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "vip");
            }
        }
        // 微店URL解析
        if (url.contains("vip.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "weidian");
            }
        }
        // 拼多多URL解析
        if (url.contains("pinduoduo.com")) {
            Pattern pattern = Pattern.compile("/(\\d+)\\.html");
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return new ProductUrlInfo(matcher.group(1), "pinduoduo");
            }
        }



        return null;
    }

    /**
     * 商品URL信息
     */
    private static class ProductUrlInfo {
        private final String productId;
        private final String platform;

        public ProductUrlInfo(String productId, String platform) {
            this.productId = productId;
            this.platform = platform;
        }

        public String getProductId() {
            return productId;
        }

        public String getPlatform() {
            return platform;
        }
    }


    private static String  getApiInvokeLanguage(String language) {
        if(language == null){
            return "en";
        }
        switch ( language) {
            case "zh":
                return "zh";
            case "fr":
                return "fr";
            case "de":
                return "de";
            case "es":
                return "es";
            case "ar":
                return "ar";
            case "en":
            default:
                return "en";
        }
    }


    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private OneBoundSearchServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    /**
     * 补充商品描述缓存
     */
//    @Async
//    public void fillProductDescCache(String source, String sourceId, String lang , String desc) {
//        // 1. 手动构造与 @Cacheable 相同的 key
////        Object key = new SimpleKey(source, sourceId, lang);
//        Object[] params = {source, sourceId, lang};
//        String cacheKey = PRODUCT_SEARCH_DETAIL + ":"+ getTenantId() +":" + Arrays.stream(params).map(Object::toString).collect(Collectors.joining(","));
//
//
//        // 2. 获取缓存
//        Cache cache = cacheManager.getCache(PRODUCT_SEARCH_DETAIL);
//        if (cache == null) {
//            log.warn("[fillProductDescCache] 缓存不存在: {}", PRODUCT_SEARCH_DETAIL);
//            return;
//        }
//
//        Cache.ValueWrapper wrapper = cache.get(cacheKey);
//        if (wrapper != null && wrapper.get() instanceof AppProductSpuDetailRespVO) {
//            AppProductSpuDetailRespVO cachedVO = (AppProductSpuDetailRespVO) wrapper.get();
//            cachedVO.setDescription(desc);
//            cache.put(cacheKey, cachedVO);
//            log.info("[fillProductDescCache] 已更新描述，sourceId: {}, source: {}", sourceId, source);
//        } else {
//            log.warn("[fillProductDescCache] 缓存未命中，key: {}", cacheKey);
//        }
//    }

    @Async
    public void fillProductDescCache(String source, String sourceId, String lang, String desc) {
        // 手动构造与 @Cacheable 相同的 key
        Object[] params = {source, sourceId, lang};
        String cacheKey = PRODUCT_SEARCH_DETAIL + ":" + getTenantId() + ":" + Arrays.stream(params).map(Object::toString).collect(Collectors.joining(","));

        // 使用 RedisTemplate 获取缓存
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        AppProductSpuDetailRespVO cachedVO = (AppProductSpuDetailRespVO) ops.get(cacheKey);

        if (cachedVO != null) {
            cachedVO.setDescription(desc);
            ops.set(cacheKey, cachedVO);
            log.info("[fillProductDescCache] 已更新描述，sourceId: {}, source: {}", sourceId, source);
        } else {
            log.warn("[fillProductDescCache] 缓存未命中，key: {}", cacheKey);
        }
    }


    /**
     * 补充商品运费缓存
     */
//    @Async
//    public void fillProductShippingFee(String source, String sourceId, String lang,  Integer fee){
//        // 构造缓存 key，必须与 @Cacheable 的 key 一致
//        String cacheKey = new org.springframework.cache.interceptor.SimpleKey(getTenantId(),source, sourceId, lang).toString();;
//        // 获取对应的缓存
//        Cache cache = cacheManager.getCache(PRODUCT_SEARCH_DETAIL);
//        if (cache != null) {
//            Cache.ValueWrapper wrapper = cache.get(cacheKey);
//            if (wrapper != null && wrapper.get() instanceof AppProductSpuDetailRespVO) {
//                AppProductSpuDetailRespVO cachedVO = (AppProductSpuDetailRespVO) wrapper.get();
//                // 更新 description 字段
//                cachedVO.setFreight(fee);
//                // 将更新后的对象重新放入缓存（保持 TTL 不变）
//                cache.put(cacheKey, cachedVO);
//                log.info("[updateProductDescriptionInCache] 已更新缓存中商品的运费，sourceId: {}, source: {}, fee: {}", sourceId, source, fee);
//            } else {
//                log.warn("[updateProductDescriptionInCache] 缓存中未找到对应数据，无法更新 description，key: {}", cacheKey);
//            }
//        }
//
//    }

    @Async
    public void fillProductShippingFee(String source, String sourceId, String lang, Integer fee) {
        // 1. 手动构造与 @Cacheable 相同的 key
        Object[] params = {source, sourceId, lang};
        String cacheKey = PRODUCT_SEARCH_DETAIL + ":"+ getTenantId() +":" + Arrays.stream(params).map(Object::toString).collect(Collectors.joining(","));


        // 2. 获取缓存
        Cache cache = cacheManager.getCache(PRODUCT_SEARCH_DETAIL);
        if (cache == null) {
            log.warn("[fillProductShippingFee] 缓存不存在: {}", PRODUCT_SEARCH_DETAIL);
            return;
        }

        Cache.ValueWrapper wrapper = cache.get(cacheKey);
        if (wrapper != null && wrapper.get() instanceof AppProductSpuDetailRespVO) {
            AppProductSpuDetailRespVO cachedVO = (AppProductSpuDetailRespVO) wrapper.get();
            cachedVO.setFreight(fee);
            cache.put(cacheKey, cachedVO);
            log.info("[fillProductShippingFee] 已更新描述，sourceId: {}, source: {},fee:{}", sourceId, source, fee);
        } else {
            log.warn("[fillProductShippingFee] 缓存未命中，key: {}", cacheKey);
        }
    }


}
