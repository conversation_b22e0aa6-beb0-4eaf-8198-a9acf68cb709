package cn.iocoder.yudao.module.product.framework.onebound.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * OneBound配置DTO - 用于解析数据库中的JSON配置
 *
 * <AUTHOR>
 */
@Data
public class OneBoundConfigDTO {

    /**
     * OneBound API基础URL
     */
    @JsonProperty("baseUrl")
    private String baseUrl;

    /**
     * API密钥
     */
    @JsonProperty("key")
    private String key;

    /**
     * API密钥
     */
    @JsonProperty("secret")
    private String secret;

    /**
     * 是否启用缓存
     */
    @JsonProperty("cacheEnabled")
    private Boolean cacheEnabled = true;

    /**
     * 连接超时时间（毫秒）
     */
    @JsonProperty("connectTimeout")
    private Integer connectTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    @JsonProperty("readTimeout")
    private Integer readTimeout = 30000;

    /**
     * 搜索结果缓存时间（秒）
     */
    @JsonProperty("searchCacheTime")
    private Integer searchCacheTime = 1800; // 30分钟

    /**
     * 商品详情缓存时间（秒）
     */
    @JsonProperty("detailCacheTime")
    private Integer detailCacheTime = 3600; // 1小时

    /**
     * 默认语言
     */
    @JsonProperty("defaultLanguage")
    private String defaultLanguage = "cn";

    /**
     * 每页最大返回结果数
     */
    @JsonProperty("maxPageSize")
    private Integer maxPageSize = 20;

    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return baseUrl != null && !baseUrl.trim().isEmpty()
                && key != null && !key.trim().isEmpty()
                && secret != null && !secret.trim().isEmpty();
    }

}
