package cn.iocoder.yudao.module.agent.enums.logistics;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 代购物流价格类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum AgentLogisticsPriceTypeEnum implements IntArrayValuable {

    /**
     * 首重续重计算
     * 传统的首重+续重递增计算方式
     */
    INCREMENTAL(1, "INCREMENTAL", "首重续重计算"),

    /**
     * 纯阶梯价格计算
     * 按重量段使用不同单价的阶梯计算方式
     */
    TIERED(2, "TIERED", "纯阶梯价格计算"),

    /**
     * 阶梯递增价格计算
     * 先分阶梯，阶梯内使用首重续重递增计算的混合方式
     */
    TIERED_INCREMENTAL(3, "TIERED_INCREMENTAL", "阶梯递增价格计算");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(AgentLogisticsPriceTypeEnum::getType).toArray();

    /**
     * 类型编码
     */
    private final Integer type;

    /**
     * 类型代码（用于数据库存储和API传输）
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据类型编码获取枚举
     *
     * @param type 类型编码
     * @return 枚举值
     */
    public static AgentLogisticsPriceTypeEnum valueOf(Integer type) {
        return Arrays.stream(values())
                .filter(item -> item.getType().equals(type))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据类型代码获取枚举
     *
     * @param code 类型代码
     * @return 枚举值
     */
    public static AgentLogisticsPriceTypeEnum valueOfCode(String code) {
        return Arrays.stream(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的类型编码
     *
     * @param type 类型编码
     * @return 是否有效
     */
    public static boolean isValid(Integer type) {
        return valueOf(type) != null;
    }

    /**
     * 判断是否为有效的类型代码
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return valueOfCode(code) != null;
    }
}
