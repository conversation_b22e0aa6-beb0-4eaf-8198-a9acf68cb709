package cn.iocoder.yudao.module.agent.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * @program: ruoyi-vue-pro
 * @description: Agent错误码枚举类
 * @author: Ding<PERSON>iao
 * @create: 2025-04-17 14:53
 **/
public interface ErrorCodeConstants {

    // ========== 代购仓库 1-300-001-000 ==========
    ErrorCode STOCK_NOT_EXISTS = new ErrorCode(1_300_001_000, "agent.stock.not.exists");

    // ========== 代购包裹订单 1-300-002-000 ==========
    ErrorCode AGENT_PARCEL_NOT_EXISTS = new ErrorCode(1_300_001_000, "agent.parcel.not.exists");
    ErrorCode AGENT_PARCEL_CANCEL_FAIL_STATUS_NOT_UNPAID = new ErrorCode(1_300_001_001, "agent.parcel.cancel_fail_status_not_unpaid");
    ErrorCode AGENT_PARCEL_DELETE_FAIL_STATUS_NOT_CANCEL = new ErrorCode(1_300_001_002, "agent.parcel.delete_fail_status_not_cancel");
    ErrorCode AGENT_PARCEL_DELIVERY_FAIL_REFUND_STATUS_NOT_NONE = new ErrorCode(1_300_001_003, "agent.parcel.deliver.fail.refund.status.not.none");
    ErrorCode AGENT_PARCEL_DELIVERY_FAIL_STATUS_NOT_UNDELIVERED = new ErrorCode(1_300_001_004, "agent.parcel.deliver.fail.status.not_undelivered");
    ErrorCode AGENT_PARCEL_UPDATE_PRICE_FAIL_PAID = new ErrorCode(1_300_001_005, "agent.parcel.update_price_fail_paid");
    ErrorCode AGENT_PARCEL_UPDATE_PRICE_FAIL_ALREADY = new ErrorCode(1_300_001_006, "agent.parcel.update_price_fail_already");
    ErrorCode AGENT_PARCEL_UPDATE_PRICE_FAIL_PRICE_ERROR = new ErrorCode(1_300_001_007, "agent.parcel.update_price_fail_price_error");
    ErrorCode AGENT_PARCEL_UPDATE_ADDRESS_FAIL_STATUS_NOT_DELIVERED = new ErrorCode(1_300_001_008, "agent.parcel.update_address_fail_status_not_delivered");
    ErrorCode AGENT_PARCEL_SUBMIT_FAIL = new ErrorCode(1_300_001_009, "agent.parcel.submit.failed.amount_incorrect");

    ErrorCode AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_ORDER_ID_ERROR = new ErrorCode(1_300_001_010,"agent.parcel.update_paid_fail_pay_order_id_error");
    ErrorCode AGENT_PARCEL_UPDATE_PAID_STATUS_NOT_UNPAID = new ErrorCode(1_300_001_011,"agent.parcel.update_paid_status_not_unpaid");
    ErrorCode AGENT_PARCEL_ITEM_UPDATE_AFTER_SALE_STATUS_FAIL = new ErrorCode(1_300_001_012,"agent.parcel_item.update_after_sale_status_fail");
    ErrorCode AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_ORDER_STATUS_NOT_SUCCESS = new ErrorCode(1_300_001_013,"agent.parcel.update_paid_fail_pay_order_status_not_success");
    ErrorCode AGENT_PARCEL_UPDATE_PAID_FAIL_PAY_PRICE_NOT_MATCH = new ErrorCode(1_300_001_014,"agent.parcel.update_paid_fail_pay_price_not_match");


    // ========== 代购转运单 1-300-003-000 ==========
    ErrorCode TRANSFER_NOT_EXISTS = new ErrorCode(1_300_002_000, "agent.transfer.not.exists");

    // ========== 代购平台仓库 1-300-004-000 ==========
    ErrorCode WAREHOUSE_NOT_EXISTS = new ErrorCode(1_300_004_000, "agent.warehouse.not.exists");

    // ========== 代购快递公司 1-300-005-000 ==========
    ErrorCode DELIVERY_EXPRESS_NOT_EXISTS = new ErrorCode(1_300_005_000, "agent.delivery.express.not.exists");

    // ========== 代购商品分类 1-300-006-000 ==========
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1_300_006_000, "agent.category.not.exists");
    ErrorCode CATEGORY_EXISTS_CHILDREN = new ErrorCode(1_300_006_001, "agent.category.exists.children");
    ErrorCode CATEGORY_PARENT_ERROR = new ErrorCode(1_300_006_001, "agent.category.parent.error"); // 不能设置自己为父代购商品分类
    ErrorCode CATEGORY_PARENT_NOT_EXITS = new ErrorCode(1_300_006_001, "agent.category.parent.not.exists");
    ErrorCode CATEGORY_PARENT_IS_CHILD = new ErrorCode(1_300_006_001, "agent.category.parent.is.child");
    ErrorCode CATEGORY_NAME_ZH_DUPLICATE = new ErrorCode(1_300_006_001, "agent.category.exists.children");


    // ========== 国际货运公司 1-*********** ==========
    ErrorCode TRANSPORT_COMPANY_NOT_EXISTS = new ErrorCode(1_300_007_000, "agent.transport.company.not.exists");

    // ========== 国际货运方案 1-*********** ==========
    ErrorCode TRANSPORT_PLAN_NOT_EXISTS = new ErrorCode(1_300_008_000, "agent.transport.plan.not.exists");
    ErrorCode TRANSPORT_PLAN_FEE_NOT_EXISTS = new ErrorCode(1_300_008_001, "agent.transport.plan.fee.not.exists");

    // ========== 代购服务项目 1-*********** ==========
    ErrorCode SERVE_NOT_EXISTS = new ErrorCode(1_300_009_000, "agent.serve.not.exists");

    // ========== 代购中心配置 1-*********** ==========
    ErrorCode CONFIG_NOT_EXISTS = new ErrorCode(1_300_010_000, "agent.config.not.exists");

    // ========== 代购售后订单 1-*********** ==========
    ErrorCode AFTER_SALE_NOT_EXISTS = new ErrorCode(1_300_011_000, "agent.after.sale.not.exists");

    // ========== 代购服务国家 1-*********** ==========
    ErrorCode COUNTRY_NOT_EXISTS = new ErrorCode(1_300_012_000, "agent.country.not.exists");

    // ========== 代购物流公司 1-*********** ==========
    ErrorCode LOGISTICS_COMPANY_NOT_EXISTS = new ErrorCode(1_300_013_000, "agent.logistics.company.not.exists");

    // ========== 代购物流公司产品 1-*********** ==========
    ErrorCode LOGISTICS_PRODUCT_NOT_EXISTS = new ErrorCode(1_300_014_000, "agent.logistics.product.not.exists");
    ErrorCode LOGISTICS_PRODUCT_PRICE_NOT_EXISTS = new ErrorCode(1_300_014_001, "agent.logistics.product.price.not.exists");
    ErrorCode LOGISTICS_PRODUCT_PRICE_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_300_014_002, "agent.logistics.product.price.import.list.is.empty");

    // ========== 代购物流国家分区 1-*********** ==========
    ErrorCode LOGISTICS_ZONE_NOT_EXISTS = new ErrorCode(1_300_015_000, "agent.logistics.zone.not.exists");
    ErrorCode LOGISTICS_ZONE_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_300_015_001, "agent.logistics.zone.import.list.is.empty");

}
