package cn.iocoder.yudao.module.agent.api.stock.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 采购入库DTO
 * @author: Ding<PERSON>iao
 * @create: 2025-04-17 20:45
 **/
@Data
public class AgentStockPurchaseInReqDTO {

    private Long orderId;

    private List<Item> items;

    private LocalDateTime inTime;

    private Integer auditStatus;

    @Data
    public static class Item {

        private Long id;

        private Long orderItemId;

        private Long categoryId;

        private Long spuId;

        private Long skuId;

        private Integer count;

        private Integer price;

        private Integer weight;

        private BigDecimal volume;
        private BigDecimal length;
        private BigDecimal width;
        private BigDecimal height;

        private List<String> inspectPicUrls;

        private List<String> inspectVideoUrls;

        private String fileUrl;

        private String remark;

    }
}
