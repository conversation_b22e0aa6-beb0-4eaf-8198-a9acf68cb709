package cn.iocoder.yudao.module.promotion.controller.admin.couponusagelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 优惠码使用记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CouponCodeUsageLogRespVO {

    @Schema(description = "优惠码编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2530")
    @ExcelProperty("优惠码编号")
    private Long id;

    @Schema(description = "优惠码模板编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7238")
    @ExcelProperty("优惠码模板编号")
    private Long templateId;

    @Schema(description = "使用的优惠码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("使用的优惠码")
    private String couponCode;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "28216")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "业务类型", converter = DictConvert.class)
    @DictFormat("promotion_coupon_business_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6450")
    @ExcelProperty("业务ID")
    private Long businessId;

    @Schema(description = "优惠金额（分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("优惠金额（分）")
    private Integer discountAmount;

    @Schema(description = "使用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("使用时间")
    private LocalDateTime useTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}