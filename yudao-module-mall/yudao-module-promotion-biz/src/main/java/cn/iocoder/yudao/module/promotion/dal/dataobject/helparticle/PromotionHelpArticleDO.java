package cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 帮助文章 DO
 *
 * <AUTHOR>
 */
@TableName(value = "promotion_help_article", autoResultMap = true)
@KeySequence("promotion_help_article_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionHelpArticleDO extends BaseDO {

    /**
     * 帮助文章编号
     */
    @TableId
    private Long id;
    /**
     * 分类编号
     */
    private Long categoryId;
    /**
     * 文章编码;用于前端路由
     */
    private String code;
    /**
     * 文章作者
     */
    private String author;
    /**
     * 浏览次数
     */
    private Integer browseCount;
    /**
     * 中文标题
     */
    private String titleZh;
    /**
     * 英文标题
     */
    private String titleEn;
    /**
     * 法语标题
     */
    private String titleFr;
    /**
     * 德语标题
     */
    private String titleDe;
    /**
     * 西班牙语标题
     */
    private String titleEs;
    /**
     * 阿拉伯语标题
     */
    private String titleAr;
    /**
     * 中文内容
     */
    private String contentZh;
    /**
     * 英文内容
     */
    private String contentEn;
    /**
     * 法语内容
     */
    private String contentFr;
    /**
     * 德语内容
     */
    private String contentDe;
    /**
     * 西班牙语内容
     */
    private String contentEs;
    /**
     * 阿拉伯语内容
     */
    private String contentAr;

    /**
     * 当前语言标题（用于多语言切换）
     */
    @TableField(exist = false)
    private String title;

    /**
     * 当前语言内容（用于多语言切换）
     */
    @TableField(exist = false)
    private String content;

    /**
     * 相关文章ID;用逗号分隔，如"1,2,3"
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> relatedArticles;
    /**
     * 有帮助反馈数量
     */
    private Integer helpfulCount;
    /**
     * 无帮助反馈数量
     */
    private Integer unhelpfulCount;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status;
    /**
     * 是否FAQ;0-否，1-是
     */
    private Boolean faq;

}