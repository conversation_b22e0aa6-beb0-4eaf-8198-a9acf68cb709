package cn.iocoder.yudao.module.promotion.controller.admin.couponcode.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 优惠券动态码分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CouponCodePageReqVO extends PageParam {

    @Schema(description = "优惠劵模板编号", example = "25367")
    private Long templateId;

    @Schema(description = "优惠码")
    private String code;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "用户编号", example = "15212")
    private Long userId;

    @Schema(description = "业务类型", example = "2")
    private Integer businessType;

    @Schema(description = "业务ID", example = "28638")
    private Long businessId;

    @Schema(description = "使用时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] useTime;

}