package cn.iocoder.yudao.module.promotion.service.article;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.promotion.controller.admin.article.vo.article.ArticleCreateReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.article.vo.article.ArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.article.vo.article.ArticleUpdateReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.article.vo.article.AppArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.article.vo.article.AppArticleRespVO;
import cn.iocoder.yudao.module.promotion.convert.article.ArticleConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.article.ArticleCategoryDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.article.ArticleDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.article.ArticleMapper;
import cn.iocoder.yudao.module.promotion.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.ARTICLE_CATEGORY_NOT_EXISTS;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.ARTICLE_NOT_EXISTS;

/**
 * 文章管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ArticleServiceImpl implements ArticleService {

    @Resource
    private ArticleMapper articleMapper;

    @Resource
    private ArticleCategoryService articleCategoryService;

    @Override
    public Long createArticle(ArticleCreateReqVO createReqVO) {
        // 校验分类存在
        validateArticleCategoryExists(createReqVO.getCategoryId());

        // 插入
        ArticleDO article = ArticleConvert.INSTANCE.convert(createReqVO);
        article.setBrowseCount(0); // 初始浏览量
        articleMapper.insert(article);
        // 返回
        return article.getId();
    }

    @Override
    public void updateArticle(ArticleUpdateReqVO updateReqVO) {
        // 校验存在
        validateArticleExists(updateReqVO.getId());
        // 校验分类存在
        validateArticleCategoryExists(updateReqVO.getCategoryId());

        // 更新
        ArticleDO updateObj = ArticleConvert.INSTANCE.convert(updateReqVO);
        articleMapper.updateById(updateObj);
    }

    @Override
    public void deleteArticle(Long id) {
        // 校验存在
        validateArticleExists(id);
        // 删除
        articleMapper.deleteById(id);
    }

    private void validateArticleExists(Long id) {
        if (articleMapper.selectById(id) == null) {
            throw exception(ARTICLE_NOT_EXISTS);
        }
    }

    private void validateArticleCategoryExists(Long categoryId) {
        ArticleCategoryDO articleCategory = articleCategoryService.getArticleCategory(categoryId);
        if (articleCategory == null) {
            throw exception(ARTICLE_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public ArticleDO getArticle(Long id) {
        return articleMapper.selectById(id);
    }

    @Override
    public ArticleDO getLastArticleByTitle(String title) {
        List<ArticleDO> articles = articleMapper.selectListByTitle(title);
        return CollUtil.getLast(articles);
    }

    @Override
    public PageResult<ArticleDO> getArticlePage(ArticlePageReqVO pageReqVO) {
        return articleMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ArticleDO> getArticleCategoryListByRecommend(Boolean recommendHot, Boolean recommendBanner) {
        return articleMapper.selectList(recommendHot, recommendBanner);
    }

    @Override
    public PageResult<AppArticleRespVO> getArticlePage(AppArticlePageReqVO pageReqVO, String language) {

        PageResult<ArticleDO> articlePageResult = articleMapper.selectPage(pageReqVO);
        List<AppArticleRespVO> appArticleRespVos = convertList(articlePageResult.getList(), article -> convertToAppArticleRespVO(article, language));
        PageResult<AppArticleRespVO> result = BeanUtils.toBean(articlePageResult, AppArticleRespVO.class);
        result.setList(appArticleRespVos);
        return result;
    }

    private AppArticleRespVO convertToAppArticleRespVO(ArticleDO article, String language) {
        AppArticleRespVO respVO = new AppArticleRespVO();
        BeanUtils.copyProperties(article, respVO);
        if(StrUtil.isEmpty(language)) {
            respVO.setTitle(article.getTitleEn());
            respVO.setContent(article.getContentEn());
            respVO.setIntroduction(article.getIntroductionEn());
        }else {
            switch (language) {
                case "zh":
                    respVO.setTitle(article.getTitleZh());
                    respVO.setContent(article.getContentZh());
                    respVO.setIntroduction(article.getIntroductionZh());
                    break;
                case "fr":
                    respVO.setTitle(article.getTitleFr());
                    respVO.setContent(article.getContentFr());
                    respVO.setIntroduction(article.getIntroductionFr());
                    break;
                case "de":
                    respVO.setTitle(article.getTitleDe());
                    respVO.setContent(article.getContentDe());
                    respVO.setIntroduction(article.getIntroductionDe());
                    break;
                case "es":
                    respVO.setTitle(article.getTitleEs());
                    respVO.setContent(article.getContentEs());
                    respVO.setIntroduction(article.getIntroductionEs());
                    break;
                case "ar":
                    respVO.setTitle(article.getTitleAr());
                    respVO.setContent(article.getContentAr());
                    respVO.setIntroduction(article.getIntroductionAr());
                case "en":
                default:
                    respVO.setTitle(article.getTitleEn());
                    respVO.setContent(article.getContentEn());
                    respVO.setIntroduction(article.getIntroductionEn());
                    break;

            }
        }
        return respVO;
    }

    @Override
    public Long getArticleCountByCategoryId(Long categoryId) {
        return articleMapper.selectCount(ArticleDO::getCategoryId, categoryId);
    }

    @Override
    public void addArticleBrowseCount(Long id) {
        // 校验文章是否存在
        validateArticleExists(id);
        // 增加浏览次数
        articleMapper.updateBrowseCount(id);
    }

    @Override
    public List<ArticleDO> getListByCategoryId(Long categoryId) {
        return articleMapper.selectList(ArticleDO::getCategoryId, categoryId);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.PROMOTION_ARTICLE_DETAIL,key = "{#id,#language}")
    public AppArticleRespVO getArticleById(Long id, String language) {
        ArticleDO articleDO = articleMapper.selectById(id);
        if (articleDO != null) {
            return convertToAppArticleRespVO(articleDO, language);
        }
        return null;
    }
}
