package cn.iocoder.yudao.module.promotion.api.coupon;


import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponCodeUseReqDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponCodeValidateReqDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponCodeValidateRespDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponRespDTO;
import cn.iocoder.yudao.module.promotion.api.coupon.dto.CouponUseReqDTO;
import cn.iocoder.yudao.module.promotion.service.coupon.CouponCodeService;
import cn.iocoder.yudao.module.promotion.service.coupon.CouponService;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeUseReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateReqBO;
import cn.iocoder.yudao.module.promotion.service.coupon.bo.CouponCodeValidateRespBO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 优惠劵 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CouponApiImpl implements CouponApi {

    @Resource
    private CouponService couponService;
    @Resource
    private CouponCodeService couponCodeService;

    @Override
    public List<CouponRespDTO> getCouponListByUserId(Long userId, Integer status) {
        return BeanUtils.toBean(couponService.getCouponList(userId, status), CouponRespDTO.class);
    }

    @Override
    public void useCoupon(CouponUseReqDTO useReqDTO) {
        couponService.useCoupon(useReqDTO.getId(), useReqDTO.getUserId(),
                useReqDTO.getOrderId());
    }

    @Override
    public void returnUsedCoupon(Long id) {
        couponService.returnUsedCoupon(id);
    }

    @Override
    public List<Long> takeCouponsByAdmin(Map<Long, Integer> giveCoupons, Long userId) {
        return couponService.takeCouponsByAdmin(giveCoupons, userId);
    }

    @Override
    public void invalidateCouponsByAdmin(List<Long> giveCouponIds, Long userId) {
        couponService.invalidateCouponsByAdmin(giveCouponIds, userId);
    }

    @Override
    public CouponCodeValidateRespDTO validateCouponCode(CouponCodeValidateReqDTO validateReqDTO) {
        // 转换请求参数
        CouponCodeValidateReqBO reqBO = BeanUtils.toBean(validateReqDTO, CouponCodeValidateReqBO.class);

        // 调用服务层验证
        CouponCodeValidateRespBO respBO = couponCodeService.validateCouponCode(reqBO);

        // 转换响应结果
        if (respBO.getValid()) {
            return CouponCodeValidateRespDTO.success(
                respBO.getTemplate().getId(),
                respBO.getTemplate().getName(),
                respBO.getDiscountAmount()
            );
        } else {
            return CouponCodeValidateRespDTO.fail(respBO.getInvalidReason());
        }
    }

    @Override
    public void useCouponCode(CouponCodeUseReqDTO useReqDTO) {
        // 转换请求参数
        CouponCodeUseReqBO reqBO = BeanUtils.toBean(useReqDTO, CouponCodeUseReqBO.class);

        // 调用服务层使用优惠码
        couponCodeService.useCouponCode(reqBO);
    }

}
