package cn.iocoder.yudao.module.promotion.dal.mysql.helparticle;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticlePageReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.helparticle.vo.AppPromotionHelpArticleSearchReqVO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helpcategory.PromotionHelpCategoryDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import cn.iocoder.yudao.module.promotion.controller.admin.helparticle.vo.*;

/**
 * 帮助文章 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PromotionHelpArticleMapper extends BaseMapperX<PromotionHelpArticleDO> {

    default PageResult<PromotionHelpArticleDO> selectPage(PromotionHelpArticlePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PromotionHelpArticleDO>()
                .eqIfPresent(PromotionHelpArticleDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(PromotionHelpArticleDO::getCode, reqVO.getCode())
                .eqIfPresent(PromotionHelpArticleDO::getTitleZh, reqVO.getTitleZh())
                .eqIfPresent(PromotionHelpArticleDO::getTitleEn, reqVO.getTitleEn())
                .eqIfPresent(PromotionHelpArticleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PromotionHelpArticleDO::getFaq, reqVO.getFaq())
                .betweenIfPresent(PromotionHelpArticleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PromotionHelpArticleDO::getId));
    }

    /**
     * 根据分类编号查询帮助文章列表，不包含内容字段
     *
     * @param categoryId 分类编号
     * @param status 状态
     * @return 帮助文章列表
     */
    default List<PromotionHelpArticleDO> selectListByCategoryIdAndStatusWithoutContent(Long categoryId, Integer status) {
        return selectList(new LambdaQueryWrapper<PromotionHelpArticleDO>()
                .select(PromotionHelpArticleDO::getId, PromotionHelpArticleDO::getCategoryId,
                        PromotionHelpArticleDO::getCode, PromotionHelpArticleDO::getAuthor,
                        PromotionHelpArticleDO::getBrowseCount, PromotionHelpArticleDO::getTitleZh,
                        PromotionHelpArticleDO::getTitleEn, PromotionHelpArticleDO::getTitleFr,
                        PromotionHelpArticleDO::getTitleDe, PromotionHelpArticleDO::getTitleEs,
                        PromotionHelpArticleDO::getTitleAr, PromotionHelpArticleDO::getRelatedArticles,
                        PromotionHelpArticleDO::getHelpfulCount, PromotionHelpArticleDO::getUnhelpfulCount,
                        PromotionHelpArticleDO::getSort, PromotionHelpArticleDO::getStatus,
                        PromotionHelpArticleDO::getFaq, PromotionHelpArticleDO::getCreateTime)
                .eq(categoryId != null, PromotionHelpArticleDO::getCategoryId, categoryId)
                .eq(status != null, PromotionHelpArticleDO::getStatus, status)
                .orderByAsc(PromotionHelpArticleDO::getSort)
                .orderByDesc(PromotionHelpArticleDO::getId));
    }

    /**
     * 查询所有启用状态的帮助文章列表，不包含内容字段
     *
     * @param status 状态
     * @return 帮助文章列表
     */
    default List<PromotionHelpArticleDO> selectListByStatusWithoutContent(Integer status) {
        return selectList(new LambdaQueryWrapper<PromotionHelpArticleDO>()
                .select(PromotionHelpArticleDO::getId, PromotionHelpArticleDO::getCategoryId,
                        PromotionHelpArticleDO::getCode, PromotionHelpArticleDO::getAuthor,
                        PromotionHelpArticleDO::getBrowseCount, PromotionHelpArticleDO::getTitleZh,
                        PromotionHelpArticleDO::getTitleEn, PromotionHelpArticleDO::getTitleFr,
                        PromotionHelpArticleDO::getTitleDe, PromotionHelpArticleDO::getTitleEs,
                        PromotionHelpArticleDO::getTitleAr, PromotionHelpArticleDO::getRelatedArticles,
                        PromotionHelpArticleDO::getHelpfulCount, PromotionHelpArticleDO::getUnhelpfulCount,
                        PromotionHelpArticleDO::getSort, PromotionHelpArticleDO::getStatus,
                        PromotionHelpArticleDO::getFaq, PromotionHelpArticleDO::getCreateTime)
                .eq(status != null, PromotionHelpArticleDO::getStatus, status)
                .orderByAsc(PromotionHelpArticleDO::getCategoryId)
                .orderByAsc(PromotionHelpArticleDO::getSort)
                .orderByDesc(PromotionHelpArticleDO::getId));
    }

    /**
     * 根据分类 ID 和文章编码查询文章
     *
     * @param categoryId 分类 ID
     * @param articleCode 文章编码
     * @param status 状态
     * @return 帮助文章
     */
    default PromotionHelpArticleDO selectByCategoryIdAndCode(Long categoryId, String articleCode, Integer status) {
        return selectOne(new LambdaQueryWrapper<PromotionHelpArticleDO>()
                .eq(PromotionHelpArticleDO::getCategoryId, categoryId)
                .eq(PromotionHelpArticleDO::getCode, articleCode)
                .eq(status != null, PromotionHelpArticleDO::getStatus, status));
    }

    /**
     * 根据关键词和语言搜索文章
     *
     * @param reqVO 搜索请求参数，包含关键词和分类 ID
     * @param language 语言代码（zh, en, fr 等）
     * @param status 状态
     * @return 帮助文章分页结果
     */
    default PageResult<PromotionHelpArticleDO> searchArticlesByLanguage(AppPromotionHelpArticleSearchReqVO reqVO, String language, Integer status) {
        LambdaQueryWrapper<PromotionHelpArticleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(status != null, PromotionHelpArticleDO::getStatus, status);

        if (reqVO.getCategoryId() != null) {
            queryWrapper.eq(PromotionHelpArticleDO::getCategoryId, reqVO.getCategoryId());
        }

        // 根据语言选择不同的字段进行搜索
        switch (language) {
            case "zh":
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleZh, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentZh, reqVO.getKeyword()));
                break;
            case "fr":
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleFr, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentFr, reqVO.getKeyword()));
                break;
            case "de":
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleDe, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentDe, reqVO.getKeyword()));
                break;
            case "es":
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleEs, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentEs, reqVO.getKeyword()));
                break;
            case "ar":
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleAr, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentAr, reqVO.getKeyword()));
                break;
            case "en":
            default:
                queryWrapper.and(wrapper -> wrapper.like(PromotionHelpArticleDO::getTitleEn, reqVO.getKeyword())
                        .or()
                        .like(PromotionHelpArticleDO::getContentEn, reqVO.getKeyword()));
                break;
        }

        // 按照排序和 ID 降序排序
        queryWrapper.orderByAsc(PromotionHelpArticleDO::getSort)
                .orderByDesc(PromotionHelpArticleDO::getId);

        // 不查询内容字段，减少数据传输量
        return selectPage(reqVO, queryWrapper.select(
                PromotionHelpArticleDO::getId, PromotionHelpArticleDO::getCategoryId,
                PromotionHelpArticleDO::getCode, PromotionHelpArticleDO::getAuthor,
                PromotionHelpArticleDO::getBrowseCount, PromotionHelpArticleDO::getTitleZh,
                PromotionHelpArticleDO::getTitleEn, PromotionHelpArticleDO::getTitleFr,
                PromotionHelpArticleDO::getTitleDe, PromotionHelpArticleDO::getTitleEs,
                PromotionHelpArticleDO::getTitleAr, PromotionHelpArticleDO::getRelatedArticles,
                PromotionHelpArticleDO::getHelpfulCount, PromotionHelpArticleDO::getUnhelpfulCount,
                PromotionHelpArticleDO::getSort, PromotionHelpArticleDO::getStatus,
                PromotionHelpArticleDO::getFaq, PromotionHelpArticleDO::getCreateTime));
    }

    /**
     * APP 端分页查询帮助文章
     *
     * @param reqVO 查询条件
     * @param status 状态
     * @return 帮助文章分页结果
     */
    default PageResult<PromotionHelpArticleDO> selectPageApp(AppPromotionHelpArticlePageReqVO reqVO, Integer status) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PromotionHelpArticleDO>()
                .eqIfPresent(PromotionHelpArticleDO::getCategoryId, reqVO.getCategoryId())
                .eqIfPresent(PromotionHelpArticleDO::getCode, reqVO.getCode())
                .eq(reqVO.getFaq() != null, PromotionHelpArticleDO::getFaq, reqVO.getFaq()) // 直接使用 eq 方法，而不是 eqIfPresent
                .eq(status != null, PromotionHelpArticleDO::getStatus, status)
                .orderByAsc(PromotionHelpArticleDO::getSort)
                .orderByDesc(PromotionHelpArticleDO::getId)
                // 不查询内容字段，减少数据传输量
                .select(
                        PromotionHelpArticleDO::getId, PromotionHelpArticleDO::getCategoryId,
                        PromotionHelpArticleDO::getCode, PromotionHelpArticleDO::getAuthor,
                        PromotionHelpArticleDO::getBrowseCount, PromotionHelpArticleDO::getTitleZh,
                        PromotionHelpArticleDO::getTitleEn, PromotionHelpArticleDO::getTitleFr,
                        PromotionHelpArticleDO::getTitleDe, PromotionHelpArticleDO::getTitleEs,
                        PromotionHelpArticleDO::getTitleAr, PromotionHelpArticleDO::getRelatedArticles,
                        PromotionHelpArticleDO::getHelpfulCount, PromotionHelpArticleDO::getUnhelpfulCount,
                        PromotionHelpArticleDO::getSort, PromotionHelpArticleDO::getStatus,
                        PromotionHelpArticleDO::getFaq, PromotionHelpArticleDO::getCreateTime));
    }
}