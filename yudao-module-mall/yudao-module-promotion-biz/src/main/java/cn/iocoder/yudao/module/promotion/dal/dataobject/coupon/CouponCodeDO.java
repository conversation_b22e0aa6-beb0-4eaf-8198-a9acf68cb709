package cn.iocoder.yudao.module.promotion.dal.dataobject.coupon;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponBusinessTypeEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 动态优惠码 DO
 *
 * <AUTHOR>
 */
@TableName(value = "promotion_coupon_code", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponCodeDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 优惠券模板编号
     *
     * 关联 {@link CouponTemplateDO#getId()}
     */
    private Long templateId;
    /**
     * 优惠码
     */
    private String code;
    /**
     * 状态
     * 
     * 1-未使用，2-已使用，3-已过期
     */
    private Integer status;
    /**
     * 使用用户ID
     */
    private Long userId;
    /**
     * 业务类型
     *
     * 枚举 {@link CouponBusinessTypeEnum}
     */
    private Integer businessType;
    /**
     * 业务ID（订单ID或代购包裹ID）
     */
    private Long businessId;
    /**
     * 优惠金额（分）
     */
    private Integer discountAmount;
    /**
     * 使用时间
     */
    private LocalDateTime useTime;
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

}
