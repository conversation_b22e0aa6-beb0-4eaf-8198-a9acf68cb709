package cn.iocoder.yudao.module.promotion.controller.admin.couponusagelog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.promotion.controller.admin.couponusagelog.vo.*;
import cn.iocoder.yudao.module.promotion.dal.dataobject.couponusagelog.CouponCodeUsageLogDO;
import cn.iocoder.yudao.module.promotion.service.couponusagelog.CouponCodeUsageLogService;

@Tag(name = "管理后台 - 优惠码使用记录")
@RestController
@RequestMapping("/promotion/coupon-code-usage-log")
@Validated
public class CouponCodeUsageLogController {

    @Resource
    private CouponCodeUsageLogService couponCodeUsageLogService;

    @PostMapping("/create")
    @Operation(summary = "创建优惠码使用记录")
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:create')")
    public CommonResult<Long> createCouponCodeUsageLog(@Valid @RequestBody CouponCodeUsageLogSaveReqVO createReqVO) {
        return success(couponCodeUsageLogService.createCouponCodeUsageLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新优惠码使用记录")
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:update')")
    public CommonResult<Boolean> updateCouponCodeUsageLog(@Valid @RequestBody CouponCodeUsageLogSaveReqVO updateReqVO) {
        couponCodeUsageLogService.updateCouponCodeUsageLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除优惠码使用记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:delete')")
    public CommonResult<Boolean> deleteCouponCodeUsageLog(@RequestParam("id") Long id) {
        couponCodeUsageLogService.deleteCouponCodeUsageLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得优惠码使用记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:query')")
    public CommonResult<CouponCodeUsageLogRespVO> getCouponCodeUsageLog(@RequestParam("id") Long id) {
        CouponCodeUsageLogDO couponCodeUsageLog = couponCodeUsageLogService.getCouponCodeUsageLog(id);
        return success(BeanUtils.toBean(couponCodeUsageLog, CouponCodeUsageLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得优惠码使用记录分页")
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:query')")
    public CommonResult<PageResult<CouponCodeUsageLogRespVO>> getCouponCodeUsageLogPage(@Valid CouponCodeUsageLogPageReqVO pageReqVO) {
        PageResult<CouponCodeUsageLogDO> pageResult = couponCodeUsageLogService.getCouponCodeUsageLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CouponCodeUsageLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出优惠码使用记录 Excel")
    @PreAuthorize("@ss.hasPermission('promotion:coupon-code-usage-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCouponCodeUsageLogExcel(@Valid CouponCodeUsageLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CouponCodeUsageLogDO> list = couponCodeUsageLogService.getCouponCodeUsageLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "优惠码使用记录.xls", "数据", CouponCodeUsageLogRespVO.class,
                        BeanUtils.toBean(list, CouponCodeUsageLogRespVO.class));
    }

}