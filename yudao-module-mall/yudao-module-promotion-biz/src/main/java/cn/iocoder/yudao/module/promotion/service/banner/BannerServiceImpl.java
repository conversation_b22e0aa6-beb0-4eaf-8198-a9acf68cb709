package cn.iocoder.yudao.module.promotion.service.banner;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.promotion.controller.admin.banner.vo.BannerCreateReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.banner.vo.BannerPageReqVO;
import cn.iocoder.yudao.module.promotion.controller.admin.banner.vo.BannerUpdateReqVO;
import cn.iocoder.yudao.module.promotion.controller.app.banner.vo.AppBannerRespVO;
import cn.iocoder.yudao.module.promotion.convert.banner.BannerConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.banner.BannerDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.helparticle.PromotionHelpArticleDO;
import cn.iocoder.yudao.module.promotion.dal.mysql.banner.BannerMapper;
import cn.iocoder.yudao.module.promotion.dal.redis.RedisKeyConstants;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.promotion.enums.ErrorCodeConstants.BANNER_NOT_EXISTS;

/**
 * 首页 banner 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BannerServiceImpl implements BannerService {

    @Resource
    private BannerMapper bannerMapper;

    @Override
    public Long createBanner(BannerCreateReqVO createReqVO) {
        // 插入
        BannerDO banner = BannerConvert.INSTANCE.convert(createReqVO);
        bannerMapper.insert(banner);
        // 返回
        return banner.getId();
    }

    @Override
    public void updateBanner(BannerUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateBannerExists(updateReqVO.getId());
        // 更新
        BannerDO updateObj = BannerConvert.INSTANCE.convert(updateReqVO);
        bannerMapper.updateById(updateObj);
    }

    @Override
    public void deleteBanner(Long id) {
        // 校验存在
        this.validateBannerExists(id);
        // 删除
        bannerMapper.deleteById(id);
    }

    private void validateBannerExists(Long id) {
        if (bannerMapper.selectById(id) == null) {
            throw exception(BANNER_NOT_EXISTS);
        }
    }

    @Override
    public BannerDO getBanner(Long id) {
        return bannerMapper.selectById(id);
    }

    @Override
    public PageResult<BannerDO> getBannerPage(BannerPageReqVO pageReqVO) {
        return bannerMapper.selectPage(pageReqVO);
    }

    @Override
    public void addBannerBrowseCount(Long id) {
        // 校验 Banner 是否存在
        validateBannerExists(id);
        // 增加点击次数
        bannerMapper.updateBrowseCount(id);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.BANNER_LIST,key = "{#position ?: 'defaultPosition',#lang}", unless = "#result == null ") //2小时缓存
    public List<AppBannerRespVO> getBannerListByPosition(Integer position, String lang) {
        List<BannerDO> banners = bannerMapper.selectBannerListByPosition(position);
        banners.forEach(banner -> processI18n(banner, lang));
        return BeanUtils.toBean(banners, AppBannerRespVO.class);
    }


    /**
     * 处理多语言
     * @param bannerDO 原始文章对象
     * @param lang 语言
     * @return 处理后的文章对象
     */
    public void processI18n(BannerDO bannerDO, String lang) {
        if (bannerDO == null) {
            return ;
        }
        // 根据语言设置标题和内容
        if (lang != null) {
            switch (lang) {
                case "zh":
                    bannerDO.setTitle(bannerDO.getTitle());
                    bannerDO.setSubTitle(bannerDO.getSubTitle());
                    break;
                case "fr":
                    bannerDO.setTitle(bannerDO.getTitleFr() != null ? bannerDO.getTitleFr() : bannerDO.getTitleEn());
                    bannerDO.setSubTitle(bannerDO.getSubTitleFr() != null ? bannerDO.getSubTitleFr() : bannerDO.getSubTitleEn());
                    break;
                case "de":
                    bannerDO.setTitle(bannerDO.getTitleDe() != null ? bannerDO.getTitleDe() : bannerDO.getTitleEn());
                    bannerDO.setSubTitle(bannerDO.getSubTitleDe() != null ? bannerDO.getSubTitleDe() : bannerDO.getSubTitleEn());
                    break;
                case "es":
                    bannerDO.setTitle(bannerDO.getTitleEs() != null ? bannerDO.getTitleEs() : bannerDO.getTitleEn());
                    bannerDO.setSubTitle(bannerDO.getSubTitleEs() != null ? bannerDO.getSubTitleEs() : bannerDO.getSubTitleEn());
                    break;
                case "ar":
                    bannerDO.setTitle(bannerDO.getTitleAr() != null ? bannerDO.getTitleAr() : bannerDO.getTitleEn());
                    bannerDO.setSubTitle(bannerDO.getSubTitleAr() != null ? bannerDO.getSubTitleAr() : bannerDO.getSubTitleEn());
                    break;
                case "en":
                default:
                    bannerDO.setTitle(bannerDO.getTitleEn());
                    bannerDO.setSubTitle(bannerDO.getSubTitleEn());
                    break;
            }
        } else {
            // 默认使用英文
            bannerDO.setTitle(bannerDO.getTitle());
            bannerDO.setSubTitle(bannerDO.getSubTitle());
        }

    }





}
