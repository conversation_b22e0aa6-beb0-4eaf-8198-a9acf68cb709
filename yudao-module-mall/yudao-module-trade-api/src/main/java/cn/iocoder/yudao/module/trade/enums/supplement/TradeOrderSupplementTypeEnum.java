package cn.iocoder.yudao.module.trade.enums.supplement;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 交易订单补款单 - 类型
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum TradeOrderSupplementTypeEnum implements IntArrayValuable {

    NORMAL_ORDER(0, "普通订单"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeOrderSupplementTypeEnum::getType).toArray();

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 类型名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }


}
