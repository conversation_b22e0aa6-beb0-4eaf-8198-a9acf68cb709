package cn.iocoder.yudao.module.trade.service.order;

import cn.iocoder.yudao.framework.tenant.config.TenantConfig;
import cn.iocoder.yudao.module.erp.api.purchase.ErpPurchaseOrderApi;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.trade.dal.dataobject.order.TradeOrderItemDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 订单代购服务接口
 * @author: DingXiao
 * @create: 2025-04-15 16:37
 **/
public interface TradeOrderAgentService {


    /**
     * 生成代购ERP数据
     *
     * @param order 订单
     * @param orderItems 订单项
     * @param config 配置
     */
    void generateErpData(TradeOrderDO order, List<TradeOrderItemDO> orderItems, TenantConfig config);


}
