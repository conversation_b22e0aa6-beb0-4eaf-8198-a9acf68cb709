package cn.iocoder.yudao.module.trade.service.price.calculator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.agent.api.serve.AgentServeApi;
import cn.iocoder.yudao.module.agent.api.serve.dto.AgentServeRespDTO;
import cn.iocoder.yudao.module.trade.service.config.TradeConfigService;
import cn.iocoder.yudao.module.trade.service.price.bo.TradePriceCalculateReqBO;
import cn.iocoder.yudao.module.trade.service.price.bo.TradePriceCalculateRespBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.tenant.core.context.TenantConfigManager.isAgentTenant;
import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantId;

/**
 * 增值服务的 {@link TradePriceCalculator} 实现类
 *
 * <AUTHOR>
 */
@Component
@Order(TradePriceCalculator.ORDER_SERVER)
@Slf4j
public class TradeServerPriceCalculator implements TradePriceCalculator {

    @Resource
    private AgentServeApi agentServeApi;

    @Resource
    private TradeConfigService tradeConfigService;

    @Override
    public void calculate(TradePriceCalculateReqBO param, TradePriceCalculateRespBO result) {
        // 0. 非代理租户，不处理 代购站才收取服务费
        if (!isAgentTenant(getTenantId())) {
            return;
        }

        // 1. 获取已选中的商品项
        List<TradePriceCalculateRespBO.OrderItem> orderItems = result.getItems();

        if (CollUtil.isEmpty(orderItems)) {
            return;
        }

        // 2. 计算收费服务费
        int totalServiceFee = 0;

        // 遍历每个商品项，计算服务费
        for (TradePriceCalculateRespBO.OrderItem orderItem : orderItems) {
            // 获取商品项的收费服务
            List<Long> chargeServices = orderItem.getChargeServices();
            if (CollUtil.isEmpty(chargeServices)) {
                continue;
            }
            // 计算当前商品项的服务费
            int itemServiceFee = 0;
            for (Long serviceId : chargeServices) {
                // 获取服务信息
                AgentServeRespDTO service = agentServeApi.getServe(serviceId);
                if (service == null) {
                    log.warn("[calculate][服务费计算失败：服务不存在 {}]", serviceId);
                    continue;
                }
                // 如果是收费服务，累加服务费
                if (ObjectUtil.equal(service.getFree(), false) && service.getPrice() != null && service.getPrice() > 0) {
                    itemServiceFee += service.getPrice() * orderItem.getCount(); // 服务费 * 商品数量
                }
            }
            // 如果当前商品项有服务费，添加到列表中
            if (itemServiceFee > 0) {
                orderItem.setServicePrice(itemServiceFee);
                TradePriceCalculatorHelper.recountPayPrice(orderItem);
                totalServiceFee += itemServiceFee;
            }
        }

        // 如果没有服务费，直接返回
        if (totalServiceFee <= 0) {
            return;
        }
        // 3. 更新订单总服务金额
        result.getPrice().setServicePrice(totalServiceFee);

        // 4. 重置总价
        TradePriceCalculatorHelper.recountAllPrice(result);
    }
}
