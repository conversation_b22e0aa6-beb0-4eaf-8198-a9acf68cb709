package cn.iocoder.yudao.module.trade.convert.supplement;

import cn.iocoder.yudao.framework.common.enums.CurrencyEnum;
import cn.iocoder.yudao.framework.common.util.string.StrUtils;
import cn.iocoder.yudao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.iocoder.yudao.module.trade.dal.dataobject.ordersupplement.TradeOrderSupplementDO;
import cn.iocoder.yudao.module.trade.enums.order.TradeMerchantOrderType;
import cn.iocoder.yudao.module.trade.framework.order.config.TradeOrderProperties;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.addTime;

/**
 * @program: ruoyi-vue-pro
 * @description:
 * @author: DingXiao
 * @create: 2025-05-25 11:07
 **/
@Mapper
public interface TradeOrderSupplementConvert {

    TradeOrderSupplementConvert INSTANCE = Mappers.getMapper(TradeOrderSupplementConvert.class);


    default PayOrderCreateReqDTO convert(TradeOrderSupplementDO order, TradeOrderProperties orderProperties) {

        PayOrderCreateReqDTO createReqDTO = new PayOrderCreateReqDTO()
                .setAppKey(orderProperties.getPayAppKey()).setUserIp("0.0.0.0");
        // 商户相关字段
        createReqDTO.setMerchantOrderId(String.valueOf(order.getId()));
        createReqDTO.setMerchantOrderType(TradeMerchantOrderType.ORDER_SUPPLEMENT);
        String subject = "订单："+order.getOrderId()+"的补款单";
        createReqDTO.setSubject(subject);
        createReqDTO.setBody(subject);
        // 订单相关字段
        createReqDTO.setPrice(order.getPayPrice())
                .setExpireTime(addTime(orderProperties.getPayExpireTime()))
                .setOrderPrice(order.getPayPrice())
                .setCurrency(CurrencyEnum.DEFAULT.getCode());
        return createReqDTO;
    }
}
