package cn.iocoder.yudao.framework.pay.core.client.impl.stripe;

import cn.iocoder.yudao.framework.pay.core.enums.channel.PayChannelEnum;
import com.stripe.param.checkout.SessionCreateParams;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * @program: ruoyi-vue-pro
 * @description: Stripe 基础客户端 未指定支付方法类型的支付客户端
 * @author: Ding<PERSON>ia<PERSON>
 * @create: 2025-02-07 18:13
 **/
@Slf4j
public class StripePcPayClient extends AbstractStripeClient{


    public StripePcPayClient(Long channelId, StripeClientConfig config) {
        super(channelId, PayChannelEnum.STRIPE_PC.getCode(), config);
    }

    @Override
    protected Optional<SessionCreateParams.PaymentMethodType> getPaymentMethodType() {
        // 不需要指定支付方法类型
        return Optional.empty();
    }

    //@Override
    //protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {
    //
    //
    //
    //    SessionCreateParams.PaymentIntentData paymentIntentData = SessionCreateParams.PaymentIntentData.builder()
    //            .putMetadata("out_trade_no", reqDTO.getOutTradeNo())
    //            .build();
    //
    //    // 创建Checkout Session参数
    //    SessionCreateParams params = SessionCreateParams.builder()
    //            .setMode(SessionCreateParams.Mode.PAYMENT)
    //            .addLineItem(SessionCreateParams.LineItem.builder()
    //                    .setQuantity(1L)
    //                    .setPriceData(SessionCreateParams.LineItem.PriceData.builder()
    //                            .setCurrency("usd")
    //                            .setUnitAmount((long)reqDTO.getPrice())
    //                            .setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
    //                                    .setName(reqDTO.getSubject())
    //                                    .build())
    //                            .build())
    //                    .build())
    //            .setSuccessUrl(reqDTO.getReturnUrl())
    //            .setCancelUrl(reqDTO.getCancelUrl())
    //            .setPaymentIntentData(paymentIntentData)
    //            .addPaymentMethodType(SessionCreateParams.PaymentMethodType.CARD)
    //            .build();
    //
    //    // 创建Checkout Session
    //    Session session = Session.create(params,requestOptions);
    //    // 获取Checkout Session的URL
    //    String checkoutUrl = session.getUrl();
    //
    //    return PayOrderRespDTO.waitingOf(PayOrderDisplayModeEnum.URL.getMode(), checkoutUrl,reqDTO.getOutTradeNo(), session.toJson());
    //}
}
