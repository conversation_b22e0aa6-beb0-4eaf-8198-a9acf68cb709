package cn.iocoder.yudao.framework.pay.core.client.impl.stripe;

import cn.iocoder.yudao.framework.pay.core.client.PayClientConfig;
import lombok.Data;

import javax.validation.Validator;
import javax.validation.constraints.NotBlank;

/**
 * @program: ruoyi-vue-pro
 * @description: Stripe配置实现类
 * @author: DingXiao
 * @create: 2025-02-07 17:09
 **/
@Data
public class StripeClientConfig implements PayClientConfig {

    ///**
    // * 单笔固定费用
    // */
    //@NotBlank(message = "单笔固定费用不能为空")
    //private Integer fixedCharge;

    /**
     * 环境模式 sandbox:沙箱  live:正式
     */
    @NotBlank(message = "环境模式")
    private String mode;

    ///**
    // * clientId
    // */
    //@NotBlank(message = "ClientId不能为空")
    //private String clientId;
    //
    ///**
    // * clientSecret
    // */
    //@NotBlank(message = "ClientSecret不能为空")
    //private String clientSecret;

    /**
     * 生产环境的密钥
     */
    @NotBlank(message = "liveSecretKey不能为空")
    private String liveSecretKey;

    /**
     * 生产环境webhook支付的密钥
     */
    @NotBlank(message = "生产环境webhookPaySecretKey不能为空")
    private String liveWebhookPaySecret;

    /**
     * 生产环境webhook退款的密钥
     */
    @NotBlank(message = "生产环境webhookRefundSecretKey不能为空")
    private String liveWebhookRefundSecret;

    /**
     * 测试环境的密钥
     */
    @NotBlank(message = "testSecretKey不能为空")
    private String testSecretKey;

    /**
     * 测试环境webhook支付的密钥
     */
    @NotBlank(message = "测试环境webhookPaySecretKey不能为空")
    private String testWebhookPaySecret;

    /**
     * 测试环境webhook退款的密钥
     */
    @NotBlank(message = "测试环境webhookRefundSecretKey不能为空")
    private String testWebhookRefundSecret;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethods;

    @Override
    public void validate(Validator validator) {

    }

}
