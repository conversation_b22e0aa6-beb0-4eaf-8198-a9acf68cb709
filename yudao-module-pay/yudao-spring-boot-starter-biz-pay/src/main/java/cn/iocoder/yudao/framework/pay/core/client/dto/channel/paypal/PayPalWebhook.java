package cn.iocoder.yudao.framework.pay.core.client.dto.channel.paypal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * PayPalWebhookDTO PayPal回调封装
 * <AUTHOR>
 */
@Data
public class PayPalWebhook {

    private String id;

    @JsonProperty("event_version")
    private String eventVersion;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("resource_type")
    private String resourceType;

    @JsonProperty("resource_version")
    private String resourceVersion;

    @JsonProperty("event_type")
    private String eventType;

    private String summary;

    private String resource;

    private List<Link> links;

    @Data
    public static class Link {

        private String href;

        private String rel;

        private String method;
    }
}

