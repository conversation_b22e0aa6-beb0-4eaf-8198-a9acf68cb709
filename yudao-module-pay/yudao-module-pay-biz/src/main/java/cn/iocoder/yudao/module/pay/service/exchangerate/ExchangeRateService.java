package cn.iocoder.yudao.module.pay.service.exchangerate;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.pay.controller.admin.exchangerate.vo.*;
import cn.iocoder.yudao.module.pay.dal.dataobject.exchangerate.ExchangeRateDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 汇率 Service 接口
 *
 * <AUTHOR>
 */
public interface ExchangeRateService {

    /**
     * 创建汇率
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createExchangeRate(@Valid ExchangeRateSaveReqVO createReqVO);

    /**
     * 更新汇率
     *
     * @param updateReqVO 更新信息
     */
    void updateExchangeRate(@Valid ExchangeRateSaveReqVO updateReqVO);

    /**
     * 删除汇率
     *
     * @param id 编号
     */
    void deleteExchangeRate(Long id);

    /**
     * 获得汇率
     *
     * @param id 编号
     * @return 汇率
     */
    ExchangeRateDO getExchangeRate(Long id);

    /**
     * 获得汇率分页
     *
     * @param pageReqVO 分页查询
     * @return 汇率分页
     */
    PageResult<ExchangeRateDO> getExchangeRatePage(ExchangeRatePageReqVO pageReqVO);

    /**
     * 获得汇率列表
     * @return 汇率列表
     */
    List<ExchangeRateDO> getExchangeRateList();

    /**
     * 根据币种获取汇率
     * @param currency 币种
     * @return 汇率
     */
    ExchangeRateDO getExchangeRateByCode(String currency);

    /**
     * 获取默认币种
     * @return 默认币种
     */
    String getDefaultCurrency();
}