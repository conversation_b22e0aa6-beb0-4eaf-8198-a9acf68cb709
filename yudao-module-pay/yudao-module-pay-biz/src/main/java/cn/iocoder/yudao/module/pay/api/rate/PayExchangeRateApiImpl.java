package cn.iocoder.yudao.module.pay.api.rate;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pay.api.rate.dto.PayExchangeRateRespDTO;
import cn.iocoder.yudao.module.pay.dal.dataobject.exchangerate.ExchangeRateDO;
import cn.iocoder.yudao.module.pay.service.exchangerate.ExchangeRateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: ruoyi-vue-pro
 * @description: 汇率API接口实现
 * @author: DingXiao
 * @create: 2025-04-10 18:31
 **/
@Service
public class PayExchangeRateApiImpl implements PayExchangeRateApi{

    @Resource
    private ExchangeRateService exchangeRateService;


    @Override
    public List<PayExchangeRateRespDTO> getExchangeRateList() {
        List<ExchangeRateDO> exchangeRateList = exchangeRateService.getExchangeRateList();
        return BeanUtils.toBean(exchangeRateList, PayExchangeRateRespDTO.class);
    }

    @Override
    public PayExchangeRateRespDTO getExchangeRate(String currency) {
        ExchangeRateDO exchangeRate = exchangeRateService.getExchangeRateByCode(currency);
        return BeanUtils.toBean(exchangeRate, PayExchangeRateRespDTO.class);
    }
}
