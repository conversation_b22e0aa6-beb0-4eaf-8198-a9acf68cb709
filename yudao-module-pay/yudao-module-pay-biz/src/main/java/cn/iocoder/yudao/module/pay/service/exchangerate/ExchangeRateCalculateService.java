package cn.iocoder.yudao.module.pay.service.exchangerate;

import java.math.BigDecimal;

/**
 * @program: ruoyi-vue-pro
 * @description: 汇率计算服务接口
 * @author: DingXiao
 * @create: 2025-05-12 19:13
 **/
public interface ExchangeRateCalculateService {

    /**
     * 计算金额
     * @param currency 币种
     * @param price 订单金额
     * @return 金额
     */
    Integer calculateAmount(String currency, Integer price);

    /**
     * 获取汇率
     * @param currency 币种
     * @return 汇率
     */
    BigDecimal getExchangeRate(String currency);

    /**
     * 获取默认币种
     * @return 默认币种
     */
    String getDefaultCurrency();


}
